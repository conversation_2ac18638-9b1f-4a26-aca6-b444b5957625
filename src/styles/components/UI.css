/* search-container */
.search-container {
    @apply sticky top-0 z-10 border border-primary-200 px-4 py-2 flex items-center;
  }
  .search-icon {
    @apply absolute left-6 top-1/2 -translate-y-1/2 text-primary-400 pointer-events-none;
  }
  .search-icon-without-container {
    @apply absolute left-3 top-1/2 -translate-y-1/2 text-primary-400 pointer-events-none;
  }
  .search-custom-input::placeholder {
    @apply text-primary-400;
  }
  /* IconButton */
  .icon-button {
    @apply flex items-center gap-2 rounded-md transition-colors duration-150 ease-in-out hover:bg-primary-100;
  }
  /* Secondary Tab */
.secondary-tab {
  @apply text-custom-md font-medium flex items-center justify-center px-4 py-2 cursor-pointer transition-all duration-200 ease-in-out border-b-2;
  white-space: nowrap;
  flex-shrink: 0;
}
.active-tab {
  @apply border-primary bg-background text-primary;
}
.inactive-tab {
  @apply bg-transparent text-muted-foreground border-transparent hover:text-foreground hover:bg-muted/20;
}

/* Custom Scrollbar Styles */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db transparent;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}

.scrollbar-track-transparent::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
}

.scrollbar-thumb-gray-400::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}