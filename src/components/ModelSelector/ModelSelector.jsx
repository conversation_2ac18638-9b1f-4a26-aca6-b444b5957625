import React, { useState, useEffect, useRef } from 'react';
import { ChevronDown, Check, Loader2 } from 'lucide-react';
import { BootstrapTooltip } from "@/components/UIComponents/ToolTip/Tooltip-material-ui";

const ModelSelector = ({ selectedModel, onSelectModel, availableModels = [], isDisabled = false, isLoading = false }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);


  const currentModel =
    selectedModel ||
    (availableModels.length > 0 ? availableModels[0] : "GPT-4.1");

  console.error({ currentModel, selectedModel, availableModels })
  return (
    <div className="relative inline-block" ref={dropdownRef}>
      <BootstrapTooltip title={isDisabled ? "Waiting for available models..." : "Select model for code generation"} placement="bottom">
        <button
          className={`flex items-center justify-between gap-1.5 px-3 py-1 typography-caption font-weight-medium bg-white border border-gray-200 rounded-md shadow-sm transition-colors min-w-[140px] h-[32px] ${
            isDisabled ? 'opacity-60 cursor-not-allowed' : 'hover:bg-gray-50 cursor-pointer'
          }`}
          onClick={() => !isDisabled && setIsOpen(!isOpen)}
          disabled={isDisabled}
        >
          {isLoading ? (
            <>
              <Loader2 size={14} className="animate-spin mr-1" />
              <span>Loading models...</span>
            </>
          ) : (
            <>
              <span className="truncate">
                {currentModel || 'Select Model'}
              </span>
              <ChevronDown size={14} className={`transition-transform ${isOpen ? 'rotate-180' : ''}`} />
            </>
          )}
        </button>
      </BootstrapTooltip>

      {isOpen && !isDisabled && (
        <div className="absolute mt-1 z-50 min-w-full bg-white rounded-md shadow-md border border-gray-200 py-1">
          <div className="px-3 py-2 border-b border-gray-200">
            <h3 className="typography-caption font-weight-semibold text-gray-600 uppercase">Model Selection</h3>
          </div>
          {availableModels.map((model) => (
            <button
              key={model}
              className="flex items-center justify-between w-full px-3 py-2 typography-caption text-left hover:bg-gray-50 transition-colors"
              onClick={() => {
                onSelectModel(model);
                setIsOpen(false);
              }}
            >
              <div className="flex flex-col">
                <span className="font-weight-medium text-gray-700">{model}</span>
              </div>
              {currentModel === model && (
                <Check size={14} className="text-primary" />
              )}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default ModelSelector;