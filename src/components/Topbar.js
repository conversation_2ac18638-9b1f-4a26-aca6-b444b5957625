"use client";
import React, { useContext, useCallback } from "react";
import { Tabs } from "@/components/Tabs";
import { TopBarContext } from "./Context/TopBarContext";

const Topbar = () => {
  const { tabs, removeTab, setActiveTab, setTabs } = useContext(TopBarContext);

  const reorder = useCallback((tabId, _fromIndex, toIndex) => {
    const beforeTab = tabs.find((tab) => tab.id === tabId);
    if (!beforeTab) {
      return;
    }
    const newTabs = tabs.filter((tab) => tab.id !== tabId);
    newTabs.splice(toIndex, 0, beforeTab);
    setTabs(newTabs);
  }, [tabs]);

  return (
    <div className="w-full overflow-x-auto bg-background border-b border-border">
      <Tabs
        onTabClose={removeTab}
        onTabReorder={reorder}
        onTabActive={setActiveTab}
        tabs={tabs}
      />
    </div>
  );
};

export default Topbar;
