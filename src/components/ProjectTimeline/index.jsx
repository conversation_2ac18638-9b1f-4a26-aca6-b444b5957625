import React, { useState, useEffect } from 'react';
import { Code2, ListTodo, ChevronRight, FolderGit2, FileTextIcon, Box, ListTodoIcon, CheckCircle, MonitorSpeaker } from "lucide-react";

// Enhanced styles for the timeline
const timelineStyles = `
  .pulse-dot-status {
    animation: pulse-dot 2s infinite;
  }

  @keyframes pulse-dot {
    0% {
      transform: scale(0.95);
      box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
    }

    70% {
      transform: scale(1);
      box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
    }

    100% {
      transform: scale(0.95);
      box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
    }
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
`;

const ProjectAssetsIcon = ({ className, size = 18, color = 'currentColor', ...props }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 14 14"
      fill="none"
      className={className}
      {...props}
    >
      <path
        d="M12.334 3.00016H9.29598L7.69598 0.866829C7.57147 0.701578 7.41036 0.567424 7.22529 0.47489C7.04022 0.382356 6.83623 0.333959 6.62932 0.333496H3.66732C3.3137 0.333496 2.97456 0.473972 2.72451 0.72402C2.47446 0.974069 2.33398 1.31321 2.33398 1.66683V2.3335H1.66732C1.3137 2.3335 0.974557 2.47397 0.724509 2.72402C0.47446 2.97407 0.333984 3.31321 0.333984 3.66683V12.3335C0.333984 12.6871 0.47446 13.0263 0.724509 13.2763C0.974557 13.5264 1.3137 13.6668 1.66732 13.6668H10.334C10.6876 13.6668 11.0267 13.5264 11.2768 13.2763C11.5268 13.0263 11.6673 12.6871 11.6673 12.3335V11.6668H12.334C12.6876 11.6668 13.0267 11.5264 13.2768 11.2763C13.5268 11.0263 13.6673 10.6871 13.6673 10.3335V4.3335C13.6673 3.97987 13.5268 3.64074 13.2768 3.39069C13.0267 3.14064 12.6876 3.00016 12.334 3.00016ZM1.66732 3.66683H4.62932L5.62932 5.00016H1.66732V3.66683ZM1.66732 12.3335V6.3335H10.334V12.3335H1.66732ZM12.334 10.3335H11.6673V6.3335C11.6673 5.97987 11.5268 5.64074 11.2768 5.39069C11.0267 5.14064 10.6876 5.00016 10.334 5.00016H7.29598L5.69598 2.86683C5.57147 2.70158 5.41036 2.56742 5.22529 2.47489C5.04022 2.38236 4.83623 2.33396 4.62932 2.3335H3.66732V1.66683H6.62932L8.42932 4.06683C8.49142 4.14963 8.57194 4.21683 8.66451 4.26311C8.75708 4.3094 8.85915 4.3335 8.96265 4.3335H12.334V10.3335Z"
        fill={color}
      />
    </svg>
  );
};
const ProjectSetupIcon = ({ className, size = 18, color = 'currentColor', ...props }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 16 16"
      fill="none"
      className={className}
      {...props}
    >
      <path
        d="M10.6527 6.66683C9.77332 6.66683 9.03198 7.23216 8.75732 8.01283L8.03865 8.00883C6.92598 8.0055 6.02065 7.10416 6.02065 6.00016V5.21083C6.80065 4.93483 7.36398 4.2015 7.36398 3.3335C7.36398 2.23083 6.45998 1.3335 5.34932 1.3335C4.23865 1.3335 3.33398 2.23083 3.33398 3.3335C3.33398 4.2015 3.89732 4.93483 4.67732 5.21083V10.7902C3.89732 11.0655 3.33398 11.7988 3.33398 12.6668C3.33398 13.7695 4.23798 14.6668 5.34865 14.6668C6.45932 14.6668 7.36332 13.7695 7.36332 12.6668C7.36332 11.7988 6.79998 11.0655 6.01998 10.7895V8.6535C6.58198 9.0775 7.27398 9.34016 8.03198 9.34283L8.76465 9.34683C9.04665 10.1142 9.78198 10.6668 10.652 10.6668C11.7633 10.6668 12.6673 9.7695 12.6673 8.66683C12.6673 7.56416 11.7633 6.66683 10.6527 6.66683ZM5.34932 13.3335C4.97865 13.3335 4.67798 13.0348 4.67798 12.6668C4.67798 12.2988 4.97932 12.0002 5.34932 12.0002C5.71932 12.0002 6.02065 12.2988 6.02065 12.6668C6.02065 13.0348 5.71932 13.3335 5.34932 13.3335ZM5.34932 4.00016C4.97865 4.00016 4.67732 3.70083 4.67732 3.3335C4.67732 2.96616 4.97865 2.66683 5.34865 2.66683C5.71865 2.66683 6.02065 2.96616 6.02065 3.3335C6.02065 3.70083 5.71932 4.00016 5.34932 4.00016ZM10.6527 9.3335C10.3467 9.3335 10.098 9.1255 10.0173 8.84683C10.0313 8.79416 10.0507 8.7435 10.0507 8.68683C10.0513 8.6215 10.0313 8.56216 10.014 8.50216C10.0893 8.21616 10.3407 8.00016 10.6527 8.00016C11.0233 8.00016 11.324 8.2995 11.324 8.66683C11.324 9.03483 11.0227 9.3335 10.6527 9.3335Z"
        fill={color}
      />
    </svg>
  );
};

const styles = `
  @keyframes pulsedot {
    0% {
      transform: scale(0.95);
      box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
    }
    70% {
      transform: scale(1);
      box-shadow: 0 0 0 6px rgba(239, 68, 68, 0);
    }
    100% {
      transform: scale(0.95);
      box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
    }
  }

  @keyframes wave {
    0% {
      transform: translate(-50%, -50%) scale(1);
      opacity: 0.8;
    }
    100% {
      transform: translate(-50%, -50%) scale(2);
      opacity: 0;
    }
  }

  .pulse-dot-status {
    position: absolute;
    top: -4px;
    right: -4px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #ef4444;
    animation: pulsedot 2s infinite;
  }

  .pulse-dot-status::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: #ef4444;
    animation: wave 2s infinite;
    z-index: -1;
  }
`;

function convertToSubActivities(items) {
  return items?.map(item => ({
    title: item.replace(/_/g, '-').replace('-', ' ').replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
  }));
}

const ProjectTimeline = ({ projectStatus, onRedirect, refetchData, isAutoConfigInProgress = false }) => {
  // Helper function to calculate completion percentage
  const calculateCompletionPercentage = (activity) => {
    if (!activity.subActivities) {
      return activity.status === 'completed' ? 100 : activity.status === 'in-progress' ? 50 : 0;
    }

    const totalSubActivities = activity.subActivities.length;
    const completedSubActivities = activity.subActivities.filter(sub => sub.status === 'completed').length;
    const inProgressSubActivities = activity.subActivities.filter(sub => sub.status === 'in-progress').length;

    return Math.round(((completedSubActivities + (inProgressSubActivities * 0.5)) / totalSubActivities) * 100);
  };

  // Helper function to get total count for an activity
  const getTotalCount = (activity) => {
    if (activity.title === "Project Assets") {
      return (projectStatus.project_assets?.repoCount || 0);
    }
    if (activity.title === "Requirements") {
      return (projectStatus.requirement?.epicCount || 0) + (projectStatus.requirement?.userStoryCount || 0);
    }
    if (activity.title === "Architecture") {
      const arch = projectStatus.architecture;
      return (arch?.requirement?.length || 0) + (arch?.systemContext?.length || 0) +
             (arch?.container?.length || 0) + (arch?.component?.length || 0) +
             (arch?.design?.length || 0) + (arch?.interface?.length || 0);
    }
    return 0;
  };

  const activities = [
    {
      icon: ProjectAssetsIcon,
      title: "Project Assets",
      description: "Map available project resources",
      status: projectStatus.project_assets?.overall,
      redirect_text: "Add Assets",
      subActivities: [
        {
          icon: FolderGit2,
          title: "Add Repo",
          description: "Add your existing repository",
          status: projectStatus.project_assets?.repo,
          count: projectStatus.project_assets?.repoCount,
        },
        {
          icon: FileTextIcon,
          title: "Add Doc",
          description: "Add your project documentation",
          status: projectStatus.project_assets?.doc
        },
        {
          icon: Box,
          title: "Add UI/UX",
          description: "Add your project ui",
          status: projectStatus.project_assets?.figma
        }
      ]
    },
    {
      icon: ProjectSetupIcon,
      title: "Project Setup",
      description: "Map your vision",
      status: projectStatus.project_setup,
    },
    {
      icon: FileTextIcon,
      title: "Requirements",
      description: "Transform needs into clear specifications",
      status: projectStatus.requirement?.overall,
      subActivities: [
        {
          icon: ListTodo,
          title: "Epics",
          description: "Design system architecture",
          status: projectStatus.requirement?.epic,
          count: projectStatus.requirement?.epicCount,
        },
        {
          icon: ListTodoIcon,
          title: "User Story",
          description: "Design system architecture",
          status: projectStatus.requirement?.userStory,
          count: projectStatus.requirement?.userStoryCount,
        }]
    },
    {
      icon: MonitorSpeaker,
      title: "Architecture",
      description: "Craft scalable system blueprints",
      status: projectStatus.architecture?.overall,
      subActivities: [
        {
          icon: Box,
          title: "Requirements",
          description: "Design system architecture",
          status: projectStatus.architecture?.requirement?.length > 0 ? "completed" : "pending",
          count: projectStatus.architecture?.requirement?.length || 0
        },
        {
          icon: Box,
          title: "SystemContext",
          description: "Design system architecture",
          status: projectStatus.architecture?.systemContext?.length > 0 ? "completed" : "pending",
          count: projectStatus.architecture?.systemContext?.length || 0
        },
        {
          icon: Box,
          title: "Container",
          description: "Design system architecture",
          status: projectStatus.architecture?.container?.length > 0 ? "completed" : "pending",
          count: projectStatus.architecture?.container?.length || 0,
          subActivities: convertToSubActivities(projectStatus.architecture?.container)
        },
        {
          icon: Box,
          title: "Component",
          description: "Design system architecture",
          status: projectStatus.architecture?.component?.length > 0 ? "completed" : "pending",
          count: projectStatus.architecture?.component?.length || 0,
          subActivities: convertToSubActivities(projectStatus.architecture?.component)
        },
        {
          icon: Box,
          title: "Design",
          description: "Design system architecture",
          status: projectStatus.architecture?.design?.length > 0 ? "completed" : "pending",
          count: projectStatus.architecture?.design?.length || 0,
          subActivities: convertToSubActivities(projectStatus.architecture?.design)
        },
        {
          icon: Box,
          title: "Interfaces",
          description: "Design system architecture",
          status: projectStatus.architecture?.interface?.length > 0 ? "completed" : "pending",
          count: projectStatus.architecture?.interface?.length || 0,
          subActivities: convertToSubActivities(projectStatus.architecture?.interface || [])
        }
      ]
    }
    ,
    {
      icon: Code2,
      title: "Code Generation",
      description: "Create project codebase",
      status: projectStatus.codegen_status == "completed" ? "completed" : projectStatus.codegen_status,
    },
  ];


  const hasActiveOrCompletedActivity = activities.some(activity =>
    activity.status === 'in-progress' || activity.status === 'completed'
  );



  React.useEffect(() => {
    const styleSheet = document.createElement("style");
    styleSheet.innerText = styles;
    document.head.appendChild(styleSheet);
    return () => styleSheet.remove();
  }, []);

  // Auto-refresh timer when autoconfiguration is in progress
  useEffect(() => {
    let autoRefreshTimer;

    if (isAutoConfigInProgress) {


      // Set up a timer that refreshes exactly every 30 seconds
      autoRefreshTimer = setInterval(() => {

        refetchData();
        setLastRefreshed(new Date());
      }, 30000); // Exactly 30 seconds
    }

    return () => {
      if (autoRefreshTimer) {
        clearInterval(autoRefreshTimer);
      }
    };
  }, [isAutoConfigInProgress, refetchData]);

  // Debug log for auto-config status
  useEffect(() => {

  }, [isAutoConfigInProgress]);

  const [expandedActivities, setExpandedActivities] = useState(() => {
    const initialState = {
      activities: activities.reduce((acc, _, index) => {
        acc[index] = false;
        return acc;
      }, {}),
      subActivities: {}
    };
    return initialState;
  });


  const toggleActivity = (index) => {
    setExpandedActivities(prev => ({
      ...prev,
      activities: {
        ...prev.activities,
        [index]: !prev.activities[index]
      }
    }));
  };

  const toggleNestedActivity = (activityIndex, subActivityIndex) => {
    setExpandedActivities(prev => ({
      ...prev,
      subActivities: {
        ...prev.subActivities,
        [`${activityIndex}-${subActivityIndex}`]: !prev.subActivities[`${activityIndex}-${subActivityIndex}`]
      }
    }));
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500';
      case 'in-progress':
        return 'bg-primary';
      case 'failed':
        return 'bg-red-500';
      default:
        return 'bg-gray-300';
    }
  };

  // Calculate overall project progress
  const overallProgress = Math.round(
    activities.reduce((sum, activity) => sum + calculateCompletionPercentage(activity), 0) / activities.length
  );

  const completedActivities = activities.filter(activity => activity.status === 'completed').length;
  const inProgressActivities = activities.filter(activity => activity.status === 'in-progress').length;
  const pendingActivities = activities.filter(activity => !activity.status || activity.status === 'pending').length;

  return (
    <div className="w-full h-full">
      {/* Inject enhanced styles */}
      <style dangerouslySetInnerHTML={{ __html: timelineStyles }} />

      {/* Compact timeline container with better spacing */}
      <div className="w-full px-4 py-3">
        {/* Enhanced Project Progress Overview */}
        <div className="space-y-4 mb-6">
          {/* Main Progress Card */}
          <div className="bg-gradient-to-r from-primary-50 to-primary-100 rounded-xl p-5 border border-primary-200">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="typography-body-lg font-weight-bold text-gray-900">Project Timeline</h3>
                <p className="typography-body-sm text-gray-600 mt-1">Track your development progress</p>
              </div>
              <div className="text-right">
                <div className="typography-heading-sm font-weight-bold text-primary-700">{overallProgress}%</div>
                <div className="typography-caption text-gray-500">Complete</div>
              </div>
            </div>

            {/* Enhanced Progress Bar */}
            <div className="relative">
              <div className="w-full bg-white/60 rounded-full h-3 shadow-inner">
                <div
                  className="h-3 rounded-full bg-gradient-to-r from-primary-500 via-primary-600 to-primary-700 shadow-sm transition-all duration-700 ease-out"
                  style={{ width: `${overallProgress}%` }}
                />
              </div>
              {overallProgress > 0 && (
                <div
                  className="absolute top-0 h-3 w-1 bg-white rounded-full shadow-sm transition-all duration-700"
                  style={{ left: `${Math.max(overallProgress - 1, 0)}%` }}
                />
              )}
            </div>
          </div>

          {/* Status Cards Grid */}
          <div className="grid grid-cols-3 gap-3">
            {/* Completed Card */}
            <div className="bg-white rounded-lg border border-green-200 p-3 hover:shadow-sm transition-shadow">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                <span className="typography-body-sm font-weight-medium text-green-700">Completed</span>
              </div>
              <div className="typography-heading-md font-weight-bold text-green-800">{completedActivities}</div>
              <div className="typography-caption text-green-600">
                {completedActivities === 1 ? 'phase' : 'phases'} done
              </div>
            </div>

            {/* In Progress Card */}
            <div className="bg-white rounded-lg border border-primary-200 p-3 hover:shadow-sm transition-shadow">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-3 h-3 rounded-full bg-primary-500 animate-pulse"></div>
                <span className="typography-body-sm font-weight-medium text-primary-700">Active</span>
              </div>
              <div className="typography-heading-md font-weight-bold text-primary-800">{inProgressActivities}</div>
              <div className="typography-caption text-primary-600">
                {inProgressActivities === 1 ? 'phase' : 'phases'} active
              </div>
            </div>

            {/* Pending Card */}
            <div className="bg-white rounded-lg border border-gray-200 p-3 hover:shadow-sm transition-shadow">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-3 h-3 rounded-full bg-gray-400"></div>
                <span className="typography-body-sm font-weight-medium text-gray-700">Pending</span>
              </div>
              <div className="typography-heading-md font-weight-bold text-gray-800">{pendingActivities}</div>
              <div className="typography-caption text-gray-600">
                {pendingActivities === 1 ? 'phase' : 'phases'} remaining
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Timeline Activities */}
        <div className="space-y-4">
          {activities.map((activity, index) => {
            const IconComponent = activity.icon;
            const hasSubActivities = activity.subActivities && activity.subActivities.length > 0;
            const isExpanded = expandedActivities.activities[index];
            const completionPercentage = calculateCompletionPercentage(activity);
            const totalCount = getTotalCount(activity);

            return (
              <div key={index} className="relative">
                {/* Timeline connector */}
                {index < activities.length - 1 && (
                  <div className={`absolute left-8 top-20 w-0.5 h-8 transition-colors duration-300 ${
                    activity.status === 'completed' ? 'bg-green-300' : 'bg-gray-200'
                  }`} />
                )}

                {/* Enhanced Activity Card */}
                <div
                  className={`relative bg-white rounded-xl border-2 transition-all duration-300 hover:shadow-lg group overflow-hidden ${
                    activity.status === 'completed' ? 'border-green-200 bg-gradient-to-r from-green-50/50 to-green-50/20' :
                    activity.status === 'in-progress' ? 'border-primary-200 bg-gradient-to-r from-primary-50/50 to-primary-50/20' :
                    'border-gray-200 hover:border-gray-300 bg-gradient-to-r from-gray-50/30 to-white'
                  } ${hasSubActivities ? 'cursor-pointer' : ''}`}
                  onClick={() => hasSubActivities && toggleActivity(index)}
                >
                  {/* Status stripe */}
                  <div className={`absolute left-0 top-0 bottom-0 w-1 ${
                    activity.status === 'completed' ? 'bg-gradient-to-b from-green-400 to-green-600' :
                    activity.status === 'in-progress' ? 'bg-gradient-to-b from-primary-400 to-primary-600' :
                    'bg-gradient-to-b from-gray-300 to-gray-400'
                  }`} />

                  <div className="p-5 pl-6">
                    <div className="flex items-start gap-4">
                      {/* Enhanced Icon with better styling */}
                      <div className="relative flex-shrink-0">
                        <div className={`relative p-3 rounded-xl shadow-sm border-2 transition-all duration-300 ${
                          activity.status === 'completed' ? 'bg-green-100 border-green-200 shadow-green-100/50' :
                          activity.status === 'in-progress' ? 'bg-primary-100 border-primary-200 shadow-primary-100/50' :
                          'bg-gray-100 border-gray-200 group-hover:bg-gray-50 shadow-gray-100/50'
                        }`}>
                          {/* Expand/collapse indicator */}
                          {hasSubActivities && (
                            <ChevronRight
                              className={`absolute -left-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 transition-transform duration-200 ${
                                isExpanded ? 'rotate-90' : ''
                              }`}
                            />
                          )}

                          {/* Status pulse indicator */}
                          {activity.status === 'in-progress' && (
                            <div className="absolute -top-1 -right-1 w-3 h-3 bg-primary-500 rounded-full animate-pulse shadow-sm" />
                          )}

                          <IconComponent
                            className="w-6 h-6"
                            color={
                              activity.status === 'completed' ? '#059669' :
                              activity.status === 'in-progress' ? 'hsl(var(--primary-600))' :
                              '#6B7280'
                            }
                          />
                        </div>
                      </div>

                      {/* Enhanced Content Area */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex-1 min-w-0">
                            <h4
                              className="typography-body-md font-weight-bold text-gray-900 truncate hover:text-primary-600 cursor-pointer transition-colors duration-200"
                              onClick={(e) => {
                                if (!hasSubActivities) {
                                  e.stopPropagation();
                                  onRedirect(activity.title);
                                }
                              }}
                            >
                              {activity.title}
                            </h4>
                            <p className="text-gray-600 mt-1 typography-body-sm line-clamp-2 leading-relaxed">
                              {activity.description}
                            </p>
                          </div>

                          {/* Enhanced Status Badges */}
                          <div className="flex items-center gap-2 flex-shrink-0 ml-4">
                            {/* Completion percentage badge */}
                            {completionPercentage > 0 && (
                              <div className={`px-2 py-1 rounded-full typography-caption font-weight-medium ${
                                activity.status === 'completed' ? 'bg-green-100 text-green-700' :
                                activity.status === 'in-progress' ? 'bg-primary-100 text-primary-700' :
                                'bg-gray-100 text-gray-600'
                              }`}>
                                {completionPercentage}%
                              </div>
                            )}

                            {/* Total count badge */}
                            {totalCount > 0 && (
                              <div className="px-2 py-1 bg-gray-100 text-gray-600 rounded-full typography-caption font-weight-medium">
                                {totalCount} {totalCount === 1 ? 'item' : 'items'}
                              </div>
                            )}

                            {/* Status indicator */}
                            <div className={`w-3 h-3 rounded-full shadow-sm ${
                              activity.status === 'completed' ? 'bg-green-500' :
                              activity.status === 'in-progress' ? 'bg-primary-500' : 'bg-gray-400'
                            }`} />
                          </div>
                        </div>

                        {/* Enhanced Progress Bar */}
                        {hasSubActivities && (
                          <div className="mt-3">
                            <div className="flex items-center justify-between mb-1">
                              <span className="typography-caption text-gray-500">Progress</span>
                              <span className="typography-caption font-weight-medium text-gray-700">
                                {completionPercentage}%
                              </span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                              <div
                                className={`h-2 rounded-full transition-all duration-500 ease-out ${
                                  activity.status === 'completed' ? 'bg-gradient-to-r from-green-400 to-green-600' :
                                  activity.status === 'in-progress' ? 'bg-gradient-to-r from-primary-400 to-primary-600' :
                                  'bg-gray-300'
                                }`}
                                style={{ width: `${completionPercentage}%` }}
                              />
                            </div>
                          </div>
                        )}

                        {/* Configuration Actions */}
                        {activity.title === "Configuration" && (
                          <div className="flex items-center gap-3 mt-4 p-3 bg-primary-50 rounded-lg border border-primary-100">
                            <button
                              className="px-3 py-1.5 bg-primary-600 text-white rounded-lg typography-body-sm font-weight-medium hover:bg-primary-700 transition-colors duration-200 shadow-sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                onRedirect('Auto Configure');
                              }}
                            >
                              Auto Configure
                            </button>
                            <span className="typography-body-sm text-gray-600">or</span>
                            <button
                              className="px-3 py-1.5 bg-white text-primary-600 border border-primary-200 rounded-lg typography-body-sm font-weight-medium hover:bg-primary-50 transition-colors duration-200"
                              onClick={(e) => {
                                e.stopPropagation();
                                onRedirect('Auto Extract');
                              }}
                            >
                              Auto Extract
                              <span className="text-gray-500 ml-1">?</span>
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                  {/* Enhanced Sub-activities section */}
                  {hasSubActivities && (
                    <div className={`mt-4 overflow-hidden transition-all duration-500 ${
                      isExpanded ? 'max-h-[5000px] opacity-100' : 'max-h-0 opacity-0'
                    }`} style={{ willChange: 'max-height, opacity' }}>
                      <div className="ml-8 space-y-2 border-l-2 border-gray-100 pl-4">
                        {activity.subActivities.map((subActivity, subIndex) => {
                          const hasNestedSubActivities = subActivity.subActivities && subActivity.subActivities.length > 0;
                          const isNestedExpanded = expandedActivities.subActivities[`${index}-${subIndex}`];

                          return (
                            <div key={`sub-${index}-${subIndex}`} className="relative">
                              {/* Enhanced Sub-activity Card */}
                              <div className={`bg-white rounded-lg border border-gray-100 p-3 hover:shadow-sm transition-all duration-200 ${
                                hasNestedSubActivities ? 'cursor-pointer' : ''
                              }`}>
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center gap-3 flex-1 min-w-0">
                                    {/* Expand/collapse for nested */}
                                    {hasNestedSubActivities && (
                                      <ChevronRight
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          toggleNestedActivity(index, subIndex);
                                        }}
                                        className={`w-4 h-4 text-gray-400 transition-transform duration-200 cursor-pointer flex-shrink-0 ${
                                          isNestedExpanded ? 'rotate-90' : ''
                                        }`}
                                      />
                                    )}

                                    {/* Sub-activity content */}
                                    <div className="flex items-center gap-2 flex-1 min-w-0">
                                      <button
                                        className="text-gray-800 hover:text-primary-600 font-weight-medium typography-body-sm truncate transition-colors duration-200"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          onRedirect(subActivity.title === "Requirements" ? "ArchitectureRequirements" : subActivity.title);
                                        }}
                                      >
                                        {subActivity.title}
                                      </button>

                                      {/* Count badge */}
                                      {(subActivity.count > 0 || subActivity.count === 0) && (
                                        <span className={`px-2 py-0.5 typography-caption font-weight-medium rounded-full flex-shrink-0 ${
                                          subActivity.count > 0 ? 'bg-gray-100 text-gray-700' : 'bg-gray-50 text-gray-400'
                                        }`}>
                                          {subActivity.count || 0}
                                        </span>
                                      )}
                                    </div>
                                  </div>

                                  {/* Enhanced status section */}
                                  {subActivity.status && (
                                    <div className="flex items-center gap-2 flex-shrink-0">
                                      <span className={`typography-caption font-weight-medium ${
                                        subActivity.status === 'completed' ? 'text-green-600' :
                                        subActivity.status === 'in-progress' ? 'text-primary-600' :
                                        'text-gray-500'
                                      }`}>
                                        {subActivity.status === 'completed' ? 'Complete' :
                                         subActivity.status === 'in-progress' ? 'Active' : 'Pending'}
                                      </span>

                                      {/* Status icon */}
                                      {subActivity.status === 'completed' ? (
                                        <CheckCircle className="w-4 h-4 text-green-500" />
                                      ) : (
                                        <div className={`w-2 h-2 rounded-full ${
                                          subActivity.status === 'in-progress' ? 'bg-primary-500' : 'bg-gray-300'
                                        }`} />
                                      )}
                                    </div>
                                  )}
                                </div>
                              </div>

                          {hasNestedSubActivities && (
                            <div className={`space-y-4 mt-1.5 overflow-hidden transition-all duration-500 ${
                              isNestedExpanded ? 'opacity-100' : 'max-h-0 opacity-0'
                            }`}>
                              {subActivity.subActivities.map((nestedActivity, nestedIndex) => (
                                <div key={`nested-${index}-${subIndex}-${nestedIndex}`} className="flex items-start group mt-4 relative">
                                  <div className="ml-5 flex-1 pl-3 min-w-0 relative">
                                    <div className="flex items-center">
                                      <div className="flex items-center justify-between w-full">
                                        <span
                                          className="truncate typography-body-sm flex-1 hover:text-primary-600 transition-colors duration-200"
                                          title={nestedActivity.title}
                                        >
                                          {nestedActivity.title}
                                        </span>
                                        {nestedActivity.status === 'completed' && (
                                          <div className="flex items-center justify-end flex-shrink-0">
                                            <div className={`w-2 h-2 rounded-full ${getStatusColor('completed')}`}></div>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                            </div>
                        );
                        })}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );

export default ProjectTimeline;