// StateContext.js
"use client"
import React, { createContext, useState, useEffect } from 'react';

export const StateContext = createContext();

export const StateProvider = ({ children }) => {
  const getInitialCollapsedState = () => {
    if (typeof window !== 'undefined') {
      return window.innerWidth < 1280;
    }
    return false;
  };

  const [isCollapsed, setIsCollapsed] = useState(true);
  const [isVertCollapse, setIsVertCollapse] = useState(true);
  const [verticalPanelState, setVerticalPanelState] = useState('closed');
  const [drawerLabel, setDrawerLabel] = useState('timeline');
  const [activeLeftPanelTab, setActiveLeftPanelTab] = useState('timeline'); // New state for left panel tabs

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const handleResize = () => {
        if (window.innerWidth < 1024 && !isCollapsed) {
          setIsCollapsed(true);
        }
      };

      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }
  }, [isCollapsed]);

  return (
    <StateContext.Provider value={{
      isCollapsed,
      setIsCollapsed,
      isVertCollapse,
      setIsVertCollapse,
      drawerLabel,
      setDrawerLabel,
      verticalPanelState,
      setVerticalPanelState,
      activeLeftPanelTab,
      setActiveLeftPanelTab
    }}>
      {children}
    </StateContext.Provider>
  );
};