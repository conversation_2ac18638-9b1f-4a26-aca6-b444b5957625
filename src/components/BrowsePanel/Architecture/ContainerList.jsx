"use client";

import React, { useEffect, useState, useContext, useCallback } from "react";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import { fetchSystemContextWithContainers, getReconfigNodeStatus, updateNodeByPriority } from "@/utils/api";
import en from "@/en.json";
import { ArchitectureContext } from "@/components/Context/ArchitectureContext";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import Badge from "@/components/UIComponents/Badge/Badge";
import ErrorView from "@/components/Modal/ErrorViewModal";
import { IconButton } from '@/components/UIComponents/Buttons/IconButton';
import { ArrowLeft, CodeXml, Eye, FolderGit2 } from "lucide-react";
import GenericCardGrid from "@/components/UIComponents/GenericCards/GenericCardGrid";
import CardGroupSkeletonLoder from "@/components/UIComponents/Loaders/CardGroupSkeletonLoder";
import RequirementsBanner from "@/components/UIComponents/Badge/RequirementBanner";
import "@/styles/tabs/architecture/container.css";
import CodeGenerationSetupModal from "@/app/modal/CodeGenerationSetupModal";
import CodeGenerationHandler from "@/app/modal/CodeGenerationHandler";
import CodeGenerationModal from "@/app/modal/CodeGenerationModal";
import { useCodeGeneration } from "@/components/Context/CodeGenerationContext";
import { PLATFORMS, frameworks, Generic, backendFrameworks, mobileFrameworks } from "@/constants/code_gen/platforms";
import { BranchSelector } from "@/components/Git/BranchSelector";
import { getRepository, listAllBranches } from "@/utils/repositoryAPI";
import RepositoryDetailsModal from "@/components/Modal/RepositoryModal";
import PastTasksModal from "@/components/Modal/PastTasksModal";
import { getPastCodeGenerationTasks, getPastCodeTasks } from "@/utils/batchAPI";
import Sessions from "@/components/Sessions/Sessions";
import Pagination from "@/components/UIComponents/Paginations/Pagination";
import { transformSessionsResponse } from "@/utils/sessionUtils";
import { Info, X, RefreshCw, AlertTriangle, CheckIcon } from "lucide-react";
import dayjs from 'dayjs';
import { buildProjectUrl } from '@/utils/navigationHelpers';
import { getRepositoryField, getProjectManifest, updateProjectManifest } from "@/utils/repositoryAPI";
import { fetchEventSource } from '@microsoft/fetch-event-source';
import Cookies from "js-cookie";

const CodeGenerationInfo = () => {
  return (
    <div className="flex items-start gap-2 bg-primary-50 rounded-md p-2 border-l-2 border-primary-400 mb-4">
      <div className="text-primary-600 flex-shrink-0 mt-0.5">
        <Info size={14} />
      </div>
      <div>
        <h2 className="text-gray-800 font-weight-medium typography-body-sm">When to use Code Generation</h2>
        <i className="text-gray-600 mt-0.5 typography-caption leading-tight">
          Generate new code components based on design specifications. Select a container and start the process
        </i>
      </div>
    </div>
  );
};

// Enhanced Manifest Preview Modal Component (adapted from maintenance page)
const ManifestPreviewModal = ({ manifest, onProceed, onCancel, isLoading, projectId, onManifestUpdate, logInfo }) => {
  const [isRegenerating, setIsRegenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState('');
  const [updatedManifest, setUpdatedManifest] = useState(manifest || '');
  const [controller, setController] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isStartingGeneration, setIsStartingGeneration] = useState(false);
  const [generationProgressText, setGenerationProgressText] = useState('');
  const { showAlert } = useContext(AlertContext);
  
  const hasNoManifest = !manifest || manifest.trim() === '';
  
  useEffect(() => {
    return () => {
      if (controller) {
        controller.abort();
      }
    };
  }, [controller]);

  useEffect(() => {
    setUpdatedManifest(manifest || '');
    setHasUnsavedChanges(false);
  }, [manifest]);

  useEffect(() => {
    if (isStartingGeneration && logInfo) {
      setGenerationProgressText(logInfo);
    }
  }, [logInfo, isStartingGeneration]);

  const handleManifestChange = (e) => {
    setUpdatedManifest(e.target.value);
    setHasUnsavedChanges(e.target.value !== (manifest || ''));
  };

  const handleSaveManifest = async () => {
    if (!updatedManifest.trim()) {
      showAlert('Manifest data is required', 'error');
      return;
    }

    setIsSaving(true);
    
    try {
      let parsedData;
      try {
        if (updatedManifest.includes('\n') && updatedManifest.includes(':')) {
          parsedData = updatedManifest;
        } else {
          parsedData = JSON.parse(updatedManifest);
        }
      } catch (parseError) {
        parsedData = updatedManifest;
      }

      const {  updateProjectManifest } = await import("@/utils/repositoryAPI");
      await updateProjectManifest(projectId,  parsedData);

      showAlert('Project manifest saved successfully!', 'success');
      setHasUnsavedChanges(false);
      setIsEditMode(false);
      
      if (typeof onManifestUpdate === 'function') {
        onManifestUpdate(updatedManifest);
      }
    } catch (error) {
      console.error('Error updating manifest:', error);
      showAlert(error.message || 'Failed to update manifest', 'error');
    } finally {
      setIsSaving(false);
    }
  };

  const handleGenerateManifest = async () => {
    setIsRegenerating(true);
    setGenerationProgress('Initializing manifest generation...');
    setUpdatedManifest('');

    const abortController = new AbortController();
    setController(abortController);

    try {
      const url = `${process.env.NEXT_PUBLIC_API_URL}/repository/generate_manifest/${projectId}/`;
      
      await fetchEventSource(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${Cookies.get('idToken')}`,
          'Content-Type': 'application/json',
          'X-Tenant-Id': Cookies.get('tenant_id'),
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache'
        },
        signal: abortController.signal,
        openWhenHidden: true,
        onopen: (response) => {
          if (response.status !== 200) {
            showAlert('Failed to connect to manifest generation service', 'error');
            setIsRegenerating(false);
            setGenerationProgress('');
            abortController.abort();
            return Promise.reject(new Error('Failed to connect'));
          }
          return Promise.resolve();
        },
        onmessage: (event) => {
          try {
            const data = JSON.parse(event.data);
            
            switch (data.status) {
              case 'starting':
              case 'progress':
                setGenerationProgress(data.message || 'Processing...');
                break;
                
              case 'streaming':
                if (data.content) {
                  setUpdatedManifest(prev => prev + data.content);
                }
                if (data.partial_manifest) {
                  setGenerationProgress('Streaming manifest content...');
                }
                break;
                
              case 'complete':
                setGenerationProgress('Generation complete!');
                if (data.manifest && data.manifest.content) {
                  setUpdatedManifest(data.manifest.content);
                }
                setTimeout(() => {
                  setIsRegenerating(false);
                  setGenerationProgress('');
                  abortController.abort();
                  setHasUnsavedChanges(true);
                  showAlert('Manifest generated successfully! Please save to apply changes.', 'success');
                }, 1000);
                break;
                
              case 'yaml_ready':
                if (data.yaml_content) {
                  setUpdatedManifest(data.yaml_content);
                }
                break;
                
              case 'error':
                showAlert(data.message || 'Failed to generate manifest', 'error');
                setGenerationProgress('');
                setIsRegenerating(false);
                abortController.abort();
                break;
                
              default:
                setGenerationProgress(data.message || 'Processing...');
            }
          } catch (parseError) {
            console.error('Error parsing manifest generation response:', parseError);
            showAlert('Error processing server response', 'error');
            setIsRegenerating(false);
            setGenerationProgress('');
            abortController.abort();
          }
        },
        onerror: (error) => {
          console.error('Manifest generation error:', error);
          showAlert('Error during manifest generation', 'error');
          setIsRegenerating(false);
          setGenerationProgress('');
          abortController.abort();
          return null;
        },
        onclose: () => {
          setController(null);
          if (isRegenerating) {
            setIsRegenerating(false);
            setGenerationProgress('');
          }
        }
      });
    } catch (error) {
      console.error('Error starting manifest generation:', error);
      showAlert(error.message || 'Failed to start manifest generation', 'error');
      setIsRegenerating(false);
      setGenerationProgress('');
      abortController.abort();
    }
  };

  const handleStopGeneration = () => {
    if (controller) {
      controller.abort();
    }
    setIsRegenerating(false);
    setGenerationProgress('');
  };

  const handleDiscardChanges = () => {
    setUpdatedManifest(manifest || '');
    setHasUnsavedChanges(false);
    setIsEditMode(false);
  };

  const handleProceedWithoutManifest = () => {
    setIsStartingGeneration(true);
    setGenerationProgressText('Starting code generation without manifest...');
    onProceed(null);
  };

  const handleProceedWithManifest = () => {
    setIsStartingGeneration(true);
    setGenerationProgressText('Starting code generation...');
    onProceed(updatedManifest || manifest);
  };

  const handleModalCancel = () => {
    if (isStartingGeneration) return;
    onCancel();
  };
  
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto">
      <div className="fixed inset-0 bg-black/60 backdrop-blur-sm" onClick={handleModalCancel} />
      <div className="relative bg-white rounded-lg shadow-xl w-full max-w-5xl mx-4 max-h-[90vh] flex flex-col border border-gray-200">
        <div className="flex items-center justify-between border-b border-gray-200 px-6 py-4 bg-gradient-to-r from-primary-50 to-primary-50">
          <h2 className="typography-heading-4 font-weight-semibold text-gray-900">
            {hasNoManifest ? 'Project Manifest Setup' : 'Project Manifest Review'}
            {isEditMode && ' - Editor'}
            {isStartingGeneration && ' - Starting Code Generation...'}
          </h2>
          <div className="flex items-center gap-3">
            <button
              className="text-gray-400 hover:text-gray-600 rounded-md p-2 hover:bg-white/80 transition-colors"
              onClick={handleModalCancel}
              aria-label="Close"
              disabled={isRegenerating || isStartingGeneration}
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>
        
        <div className="px-6 py-4 flex-1 overflow-y-auto">
          {isStartingGeneration && (
            <div className="fixed inset-0 bg-gray-900/50 backdrop-blur-sm flex items-center justify-center z-[60]">
              <div className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl mx-4 relative overflow-hidden">
                <div className="absolute top-0 left-0 w-full h-2 bg-gray-100">
                  <div className="h-full bg-primary transition-all duration-500 w-3/4 animate-pulse" />
                </div>

                <div className="p-8 pt-12">
                  <div className="text-center mb-8">
                    <h2 className="typography-heading-2 font-weight-bold text-gray-900">Starting Code Generation</h2>
                    <p className="mt-2 text-gray-600">
                      Please wait while we prepare your code generation environment
                    </p>
                  </div>

                  <div className="flex flex-col items-center gap-6">
                    <div className="rounded-full bg-primary-100 p-6">
                      <RefreshCw className="w-12 h-12 animate-spin text-primary-600" />
                    </div>
                    <div className="text-center">
                      <span className="typography-body text-primary-800 font-weight-medium">
                        {generationProgressText || 'Initializing code generation process...'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {!isStartingGeneration && hasNoManifest ? (
            <div className="mb-6">
              <div className="flex items-start gap-3 p-4 bg-amber-50 rounded-lg border border-amber-200 mb-4">
                <AlertTriangle className="w-5 h-5 text-amber-600 flex-shrink-0 mt-0.5" />
                <div>
                  <h3 className="typography-body font-weight-semibold text-amber-800 mb-2">
                    No Project Manifest Found
                  </h3>
                  <p className="typography-body-sm text-amber-700 mb-3">
                    A project manifest helps understand your project structure and guides the code generation process more effectively. 
                    Without it, the generation process may be less targeted.
                  </p>
                  <div className="flex flex-col gap-2">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                      <span className="typography-body-sm text-amber-700">Generate a manifest for better results</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                      <span className="typography-body-sm text-amber-700">Or proceed without manifest</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="typography-body font-weight-medium text-gray-800">
                    Create Project Manifest
                  </h3>
                  <div className="flex items-center gap-2">
                    {!isRegenerating && (
                      <button
                        onClick={handleGenerateManifest}
                        className="typography-body-sm text-primary-600 hover:text-primary-700 underline font-weight-medium"
                      >
                        Generate Manifest
                      </button>
                    )}
                    {isRegenerating && (
                      <button
                        onClick={handleStopGeneration}
                        className="px-3 py-1 typography-body-sm bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center gap-2"
                      >
                        <X className="w-3 h-3" />
                        Stop
                      </button>
                    )}
                  </div>
                </div>
                
                {isRegenerating && generationProgress && (
                  <div className="mb-4 p-3 bg-gradient-to-r from-primary-50 to-primary-50 rounded border border-primary-200">
                    <div className="flex items-center gap-3">
                      <RefreshCw className="w-4 h-4 animate-spin text-primary-600" />
                      <span className="typography-body-sm text-primary-800 font-weight-medium">
                        {generationProgress}
                      </span>
                    </div>
                  </div>
                )}
                
                <textarea
                  value={updatedManifest}
                  onChange={handleManifestChange}
                  className="w-full min-h-[400px] max-h-[500px] p-3 border rounded font-mono text-sm bg-white resize-none focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Generate a manifest or manually enter project manifest data in YAML or JSON format..."
                  disabled={isRegenerating}
                />
              </div>

              {updatedManifest.trim() !== '' && (
                <div className="mt-3 flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
                  <div className="flex items-center gap-2">
                    <CheckIcon className="w-4 h-4 text-green-600" />
                    <span className="typography-body-sm text-green-800 font-medium">
                      Manifest is ready to save and use for code generation.
                    </span>
                  </div>
                  <button
                    onClick={handleSaveManifest}
                    disabled={isSaving}
                    className={`flex items-center gap-2 px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
                      isSaving
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-green-600 hover:bg-green-700 text-white shadow-sm'
                    }`}
                  >
                    {isSaving ? (
                      <RefreshCw className="w-3 h-3 animate-spin" />
                    ) : (
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12" />
                      </svg>
                    )}
                    <span>{isSaving ? 'Saving...' : 'Save Manifest'}</span>
                  </button>
                </div>
              )}
            </div>
          ) : !isStartingGeneration ? (
            <div className="mb-4">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="typography-body font-weight-medium text-green-700">
                  Project manifest found and will be used for code generation
                </span>
              </div>
              <p className="typography-body-sm text-gray-600 mb-4">
                The following manifest will be used to understand your project structure and guide the code generation process.
              </p>

              {isRegenerating && generationProgress && (
                <div className="mb-4 p-4 bg-gradient-to-r from-primary-50 to-primary-50 rounded-lg border border-primary-200">
                  <div className="flex items-center gap-3">
                    <RefreshCw className="w-5 h-5 animate-spin text-primary-600" />
                    <span className="typography-body text-primary-800 font-weight-medium">
                      {generationProgress}
                    </span>
                  </div>
                </div>
              )}
              
              <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="typography-body font-weight-medium text-gray-800">
                    {isEditMode ? 'Edit Manifest' : 'Current Manifest'}
                  </h3>
                  <div className="flex items-center gap-2">
                    {!isRegenerating && !isEditMode && (
                      <>
                        <button
                          onClick={() => setIsEditMode(true)}
                          className="typography-body-sm text-primary-600 hover:text-primary-700 underline font-weight-medium"
                        >
                          Edit Manifest
                        </button>
                        <button
                          onClick={handleGenerateManifest}
                          className="typography-body-sm text-primary-600 hover:text-primary-700 underline font-weight-medium"
                        >
                          Regenerate Manifest
                        </button>
                      </>
                    )}
                    {isEditMode && (
                      <button
                        onClick={handleDiscardChanges}
                        className="typography-body-sm text-gray-600 hover:text-gray-700 underline font-weight-medium"
                      >
                        Cancel
                      </button>
                    )}
                    {isRegenerating && (
                      <button
                        onClick={handleStopGeneration}
                        className="px-3 py-1 typography-body-sm bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center gap-2"
                      >
                        <X className="w-3 h-3" />
                        Stop
                      </button>
                    )}
                  </div>
                </div>
                
                {isEditMode ? (
                  <textarea
                    value={updatedManifest}
                    onChange={handleManifestChange}
                    className="w-full min-h-[500px] max-h-[600px] p-3 border rounded font-mono text-sm bg-white resize-none focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Enter project manifest data in YAML or JSON format..."
                    disabled={isRegenerating}
                  />
                ) : (
                  <pre className="typography-body-sm font-mono text-gray-700 bg-white rounded border p-3 overflow-x-auto whitespace-pre-wrap min-h-[500px] max-h-[600px] overflow-y-auto">
                    {updatedManifest || manifest}
                  </pre>
                )}
              </div>

              {hasUnsavedChanges && (
                <div className="mt-3 flex items-center justify-between p-3 bg-amber-50 rounded-lg border border-amber-200">
                  <div className="flex items-center gap-2">
                    <svg className="w-4 h-4 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                    <span className="typography-body-sm text-amber-800 font-medium">
                      {isEditMode ? 'You have unsaved changes.' : 'Generated manifest is ready to save.'}
                    </span>
                  </div>
                  {!isEditMode && hasUnsavedChanges && (
                    <button
                      onClick={handleSaveManifest}
                      disabled={isSaving}
                      className={`flex items-center gap-2 px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
                        isSaving
                          ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                          : 'bg-amber-600 hover:bg-amber-700 text-white shadow-sm'
                      }`}
                    >
                      {isSaving ? (
                        <RefreshCw className="w-3 h-3 animate-spin" />
                      ) : (
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12" />
                        </svg>
                      )}
                      <span>{isSaving ? 'Saving...' : 'Save Now'}</span>
                    </button>
                  )}
                </div>
              )}
            </div>
          ) : null}
        </div>
        
        <div className="flex justify-end gap-3 px-6 py-4 border-t border-gray-200 bg-gradient-to-r from-gray-50 to-primary-50/30">
          <button
            type="button"
            className="px-6 py-2 typography-body-sm bg-white text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors shadow-sm"
            onClick={handleModalCancel}
            disabled={isLoading || isRegenerating || isStartingGeneration}
          >
            Cancel
          </button>
          
          {!isStartingGeneration && hasNoManifest && updatedManifest.trim() === '' && (
            <button
              type="button"
              className={`px-6 py-2 typography-body-sm rounded-lg flex items-center gap-2 shadow-sm transition-colors ${
                isLoading || isRegenerating
                  ? "bg-gray-300 cursor-not-allowed text-gray-500"
                  : "bg-amber-600 hover:bg-amber-700 text-white"
              }`}
              onClick={handleProceedWithoutManifest}
              disabled={isLoading || isRegenerating}
              title="Proceed without manifest"
            >
              <AlertTriangle className="w-4 h-4" />
              <span>Proceed Without Manifest</span>
            </button>
          )}
          
          {!isStartingGeneration && hasUnsavedChanges && (
            <button
              type="button"
              onClick={handleSaveManifest}
              disabled={isSaving}
              className={`px-6 py-2 typography-body-sm rounded-lg flex items-center gap-2 shadow-sm transition-colors ${
                isSaving
                  ? "bg-amber-300 cursor-not-allowed text-amber-700"
                  : "bg-amber-600 hover:bg-amber-700 text-white"
              }`}
            >
              {isSaving ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12" />
                </svg>
              )}
              <span>{isSaving ? 'Saving...' : 'Save Changes'}</span>
            </button>
          )}
          
          {!isStartingGeneration && (updatedManifest.trim() !== '' || !hasNoManifest) && (
            <button
              type="button"
              className={`px-6 py-2 typography-body-sm text-white rounded-lg flex items-center gap-2 shadow-sm transition-colors ${
                isLoading || isRegenerating || hasUnsavedChanges
                  ? "bg-primary-300 cursor-not-allowed"
                  : "bg-primary-600 hover:bg-primary-700"
              }`}
              onClick={handleProceedWithManifest}
              disabled={isLoading || isRegenerating || hasUnsavedChanges}
              title={hasUnsavedChanges ? "Please save your changes before proceeding" : ""}
            >
              {isLoading && <RefreshCw className="w-4 h-4 animate-spin" />}
              {isLoading ? 'Starting...' : 'Proceed with Code Generation'}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

const ContainerList = ({isModal=false}) => {
  const { showAlert } = useContext(AlertContext);
  const [loading, setLoading] = useState(true);
  const [containerList, setContainerList] = useState([]);
  const { setContainerData } = useContext(ArchitectureContext);
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const projectId = pathname.split("/")[3];
  const router = useRouter();
  const [error, setError] = useState(null);
  const [showBaner, setShowBaner] = useState(false);
  const [reconfigCount, setReconfigCount] = useState(0);

  // Code generation state variables
  const [codeGenSetupModal, setCodeGenSetupModal] = useState(false);
  const [isGeneratingCode, setIsGeneratingCode] = useState(false);
  const [currentPlatform, setCurrentPlatform] = useState({ key: "generic", label: "Generic", icon: <Generic /> });
  const [currentFramework, setCurrentFramework] = useState(frameworks[0]);
  const [selectedContainer, setSelectedContainer] = useState({});
  const [selectedContainerId, setSelectedContainerId] = useState("");
  const { isVisible } = useCodeGeneration();

  // Multi-select state for code generation
  const [selectedContainers, setSelectedContainers] = useState({
    all_containers: true,
    containers: []
  });
  const [isAllSelected, setIsAllSelected] = useState(true);

  // Repository state variables
  const [showRepoDetails, setShowRepoDetails] = useState(false);
  const [isRepoConfigured, setIsRepoConfigured] = useState(false);
  const [repositoryState, setRepositoryState] = useState({
    state: 'initial',
    data: null
  });

  // Loading states for individual buttons
  const [loadingStates, setLoadingStates] = useState({});

  // Branch management state
  const [containerBranches, setContainerBranches] = useState({});

  // Past tasks state variables
  const [isPastTasksCodeModalOpen, setIsPastTasksCodeModalOpen] = useState(false);
  const [pastTasks, setPastTasks] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [limit, setLimit] = useState(10);
  const [skip, setSkip] = useState(0);
  const [isPastTaskLoading, setIsPastTaskLoading] = useState(false);

  // History modal state variables
  const [sessions, setSessions] = useState([]);
  const [isHistoryLoading, setIsHistoryLoading] = useState(true);
  const [isHistoryOpen, setIsHistoryOpen] = useState(false);
  const [historyPagination, setHistoryPagination] = useState({
    currentPage: 1,
    pageSize: 10,
    totalItems: 0
  });
  const [historyFilters, setHistoryFilters] = useState({
    search: '',
    type: null,
    status: null,
    created_at: null
  });

  // Manifest related state
  const [manifestData, setManifestData] = useState(null);
  const [isCheckingManifest, setIsCheckingManifest] = useState(false);
  const [showManifestPreview, setShowManifestPreview] = useState(false);
  const [manifestLogInfo, setManifestLogInfo] = useState('');

  const checkProjectManifest = async () => {
    try {
      setIsCheckingManifest(true);
      let containerIds = selectedContainers.all_containers 
            ? containerList.data.containers.map(c => c.id)
            : selectedContainers.containers.map(id => parseInt(id));
      const response = await getProjectManifest(projectId, containerIds);
      
      if (response.field_value) {
        const manifestContent = typeof response.field_value === 'string' 
          ? response.field_value 
          : JSON.stringify(response.field_value, null, 2);
        setManifestData(manifestContent);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error checking manifest:', error);
      return false;
    } finally {
      setIsCheckingManifest(false);
    }
  };

  const proceedWithCodeGeneration = async (updatedManifest = null) => {
    setShowManifestPreview(false);
    setManifestLogInfo('');
    
    // Get selected container IDs
    const containerIds = selectedContainers.all_containers 
      ? containerList.data.containers.map(c => c.id)
      : selectedContainers.containers.map(id => parseInt(id));

    if (containerIds.length === 0) {
      showAlert('Please select at least one container', 'error');
      return;
    }

    // Start actual code generation with new API structure
    setIsGeneratingCode(true);
  };

  const fetchData = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await fetchSystemContextWithContainers(projectId);


      let filteredContainers = data?.data?.containers?.filter(
        item => item.properties?.ContainerType?.toLowerCase() == "internal" || item.properties?.ContainerType === undefined
      );
      let filteredData = { ...data, data: { ...data.data, containers: filteredContainers } };
      sessionStorage.setItem(
        "project name",
        data?.data?.systemContext?.properties?.Title
      );

      const reconfig = await getReconfigNodeStatus(projectId);
      let showBanner = false;

      if (reconfig) {
        const hasReconfigNeeded =
        reconfig.Container?.some(item => item.reconfig_needed === true);
        const Count =
        (reconfig.Container?.filter(item => item.reconfig_needed === true)?.length || 0);

        setReconfigCount(Count);
        showBanner = hasReconfigNeeded;
      }
      setShowBaner(showBanner);

      setContainerList(filteredData);
      setContainerData(filteredData?.data?.containers);
    } catch (error) {
      setError(error);
    } finally {
      setLoading(false);
    }
  };

  // Function to fetch all branches for a specific container
  const fetchAllBranchesForContainer = async (containerId, onComplete = null) => {
    const containerKey = `container_${containerId}`;
    
    try {
      setContainerBranches(prev => ({
        ...prev,
        [containerKey]: {
          ...prev[containerKey],
          isFetching: true,
          error: false
        }
      }));

      let allBranches = [];
      let currentPage = 1;
      let totalPages = 1;
      const perPage = 100;

      do {
        const response = await listAllBranches(projectId, containerId, currentPage, perPage);
        
        if (response?.detail === "404: Repository not found") {
          setContainerBranches(prev => ({
            ...prev,
            [containerKey]: {
              branches: [],
              allBranchesFetched: true,
              isFetching: false,
              error: false,
              pagination: { currentPage: 1, totalPages: 1, perPage, totalCount: 0 }
            }
          }));
          return [];
        }
        
        if (response.branches && response.branches.length > 0) {
          allBranches = [...allBranches, ...response.branches];
        }
        
        totalPages = response.pagination?.total_pages || 1;
        currentPage++;
      } while (currentPage <= totalPages);

      setContainerBranches(prev => ({
        ...prev,
        [containerKey]: {
          branches: allBranches,
          allBranchesFetched: true,
          isFetching: onComplete ? true : false,
          error: false,
          pagination: {
            currentPage: 1,
            totalPages: Math.ceil(allBranches.length / 30),
            perPage: 30,
            totalCount: allBranches.length
          }
        }
      }));

      if (onComplete) {
        await onComplete();
        setContainerBranches(prev => ({
          ...prev,
          [containerKey]: {
            ...prev[containerKey],
            isFetching: false
          }
        }));
      }

      return allBranches;
    } catch (error) {
      console.error("Error fetching all branches:", error);
      setContainerBranches(prev => ({
        ...prev,
        [containerKey]: {
          branches: [],
          allBranchesFetched: true,
          isFetching: false,
          error: false,
          pagination: { currentPage: 1, totalPages: 1, perPage: 30, totalCount: 0 }
        }
      }));
      return [];
    }
  };

  const getPaginatedBranches = (containerId, page = 1, pageSize = 30) => {
    const containerKey = `container_${containerId}`;
    const containerData = containerBranches[containerKey];
    
    if (!containerData || !containerData.branches) {
      return {
        branches: [],
        pagination: { currentPage: 1, totalPages: 1, perPage: pageSize, totalCount: 0 }
      };
    }

    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedBranches = containerData.branches.slice(startIndex, endIndex);
    
    return {
      branches: paginatedBranches,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(containerData.branches.length / pageSize),
        perPage: pageSize,
        totalCount: containerData.branches.length
      }
    };
  };

  const fetchRepositoryDetails = async (containerId, actionType = 'view') => {
    const loadingKey = `${containerId}-${actionType}`;
    
    try {
      setLoadingStates(prev => ({ ...prev, [loadingKey]: true }));
      
      setRepositoryState({
        state: 'loading',
        data: null
      });

      const [repoResponse, allBranches] = await Promise.all([
        getRepository(projectId, containerId),
        fetchAllBranchesForContainer(containerId)
      ]);

      if (repoResponse.repository) {
        setRepositoryState({
          state: 'success',
          data: repoResponse.repository
        });
        setIsRepoConfigured(true);
        
        if (actionType === 'config') {
          onConfigureContainer(containerList.data.containers.find(c => c.id === containerId));
        } else if (actionType === 'view') {
          setShowRepoDetails(true);
        }
      } else {
        setRepositoryState({
          state: 'error',
          data: null
        });
        setIsRepoConfigured(false);
        
        if (actionType === 'config') {
          onConfigureContainer(containerList.data.containers.find(c => c.id === containerId));
        } else if (actionType === 'view') {
          setShowRepoDetails(true);
        }
      }
    } catch (err) {
      setRepositoryState({
        state: 'error',
        data: null
      });
      setIsRepoConfigured(false);
      console.error("Error fetching repository details:", err);
      
      if (actionType === 'config') {
        onConfigureContainer(containerList.data.containers.find(c => c.id === containerId));
      } else if (actionType === 'view') {
        setShowRepoDetails(true);
      }
    } finally {
      setLoadingStates(prev => ({ ...prev, [loadingKey]: false }));
    }
  };

  const handlePropertyUpdate = async (key, value) => {
    try {
      if (!selectedContainerId) {
        console.warn('handlePropertyUpdate called with empty selectedContainerId', { key, value });
        return;
      }

      const response = await updateNodeByPriority(selectedContainerId, key, value);

      if (response === "success") {
        setSelectedContainer(prev => ({
          ...prev,
          properties: {
            ...prev.properties,
            [key]: value
          }
        }));

        setContainerList(prevList => {
          const updatedContainers = prevList.data.containers.map(container => {
            if (container.id === selectedContainerId) {
              return {
                ...container,
                properties: {
                  ...container.properties,
                  [key]: value
                }
              };
            }
            return container;
          });

          return {
            ...prevList,
            data: {
              ...prevList.data,
              containers: updatedContainers
            }
          };
        });

      } else {
        throw new Error('Update failed');
      }
    } catch (error) {
      console.error('Error in handlePropertyUpdate:', error, { selectedContainerId, key, value });
      showAlert("Failed to update content", "error");
    }
  };

  const handleRowClick = (containerId) => {
    router.push(buildProjectUrl(projectId, `architecture/container/${containerId}`));
  };

  const handleRepoChangeInModal = (updatedRepo) => {
    setRepositoryState({
      state: 'success',
      data: updatedRepo
    });
    
    setSelectedContainer(prev => ({
      ...prev,
      repositoryName: updatedRepo.repositoryName,
      repository_name: updatedRepo.repositoryName,
      project_name: updatedRepo.repositoryName,
      branch: updatedRepo.branch || prev.properties?.branch || null,
      properties: {
        ...prev.properties,
        repositoryName: updatedRepo.repositoryName,
        repository_name: updatedRepo.repositoryName,
        project_name: updatedRepo.repositoryName,
        branch: updatedRepo.branch || prev.properties?.branch || null
      }
    }));

    setContainerList(prevList => {
      const updatedContainers = prevList.data.containers.map(container => {
        if (container.id === selectedContainerId) {
          return {
            ...container,
            repositoryName: updatedRepo.repositoryName,
            repository_name: updatedRepo.repositoryName,
            project_name: updatedRepo.repositoryName,
            branch: updatedRepo.branch || container.properties?.branch || null,
            properties: {
              ...container.properties,
              repositoryName: updatedRepo.repositoryName,
              repository_name: updatedRepo.repositoryName,
              project_name: updatedRepo.repositoryName,
              branch: updatedRepo.branch || container.properties?.branch || null
            }
          };
        }
        return container;
      });

      return {
        ...prevList,
        data: {
          ...prevList.data,
          containers: updatedContainers
        }
      };
    });

    if (selectedContainerId && updatedRepo.branch) {
      fetchAllBranchesForContainer(selectedContainerId);
    }

    showAlert('Repository configured successfully', 'success');
  };

  const onConfigureContainer = (container) => {
    const updatedContainer = containerList.data.containers.find(c => c.id === container.id) || container;
    setSelectedContainer(updatedContainer);
    setSelectedContainerId(updatedContainer.id);

    if (updatedContainer.properties.platform) {
      const platformData = PLATFORMS.find(p => p.key === updatedContainer.properties.platform);
      setCurrentPlatform(platformData ? {
        key: platformData.key,
        label: platformData.label,
        icon: platformData.icon
      } : { key: "generic", label: "Generic", icon: <Generic /> });
    } else {
      setCurrentPlatform({ key: "generic", label: "Generic", icon: <Generic /> });
    }

    if (updatedContainer.properties.framework) {
      const frameworkData = frameworks.find(f => f.key === updatedContainer.properties.framework) ||
                          backendFrameworks.find(f => f.key === updatedContainer.properties.framework) ||
                          mobileFrameworks.find(f => f.key === updatedContainer.properties.framework);
      if (frameworkData) {
        setCurrentFramework(frameworkData);
      } else {
        setCurrentFramework(frameworks[0]);
      }
    } else {
      setCurrentFramework(frameworks[0]);
    }

    setCodeGenSetupModal(true);
  };

  const handleUpdateConfiguration = () => {
    fetchData();
    setCodeGenSetupModal(false);
    showAlert('Configuration updated successfully', 'success');
  };

  const handleStartCodeGeneration = async () => {
    if (!selectedContainers.all_containers && selectedContainers.containers.length === 0) {
      showAlert('Please select at least one container', 'error');
      return;
    }

    // Check for manifest before starting code generation
    const hasManifest = await checkProjectManifest();
    setShowManifestPreview(true);
  };

  const handlePlatformChange = (platformData) => {
    setCurrentPlatform(platformData);

    if (selectedContainerId) {
      handlePropertyUpdate("platform", platformData.key);
    }

    if (platformData.key === "mobile") {
      setCurrentFramework(mobileFrameworks[0]);
      if (selectedContainerId) {
        handlePropertyUpdate("framework", mobileFrameworks[0].key);
      }
    } else if (platformData.key === "web") {
      setCurrentFramework(frameworks[0]);
      if (selectedContainerId) {
        handlePropertyUpdate("framework", frameworks[0].key);
      }
    } else if (platformData.key === "backend") {
      setCurrentFramework(backendFrameworks[0]);
      if (selectedContainerId) {
        handlePropertyUpdate("framework", backendFrameworks[0].key);
      }
    } else {
      setCurrentFramework(frameworks[0]);
      if (selectedContainerId) {
        handlePropertyUpdate("framework", frameworks[0].key);
      }
    }
  };

  const handleFrameworkChange = (newFramework) => {
    setCurrentFramework(newFramework);

    if (selectedContainerId) {
      handlePropertyUpdate("framework", newFramework.key);
    }
  };

  const handleBranchUpdate = async (newBranch) => {
    try {
      await handlePropertyUpdate("branch", newBranch);
    } catch (error) {
      console.error(error);
      showAlert('Failed to update branch', 'error');
    }
  };

  const BranchSelection = () => {
    if (!selectedContainerId) {
      console.warn('BranchSelection rendered without selectedContainerId');
    }

    const containerKey = `container_${selectedContainerId}`;
    const containerData = containerBranches[containerKey] || {
      branches: [],
      isFetching: false,
      error: false,
      pagination: { currentPage: 1, totalPages: 1, perPage: 30, totalCount: 0 }
    };

    const handleFetchBranches = async (page = 1) => {
      return getPaginatedBranches(selectedContainerId, page);
    };

    const handleForceRefreshBranches = async (newBranchName = null) => {
      if (selectedContainerId) {
        await fetchAllBranchesForContainer(selectedContainerId, async () => {
          await new Promise(resolve => setTimeout(resolve, 300));
          
          if (newBranchName) {
            await handlePropertyUpdate("branch", newBranchName);
          }
        });
      }
    };

    const { branches: paginatedBranches, pagination } = getPaginatedBranches(selectedContainerId, containerData.pagination?.currentPage || 1);

    const currentContainer = containerList?.data?.containers?.find(c => c.id === selectedContainerId);
    const currentBranch = currentContainer?.properties?.branch || selectedContainer?.properties?.branch || null;

    return (
      <BranchSelector
        key={`branch-selector-${selectedContainerId || 'default'}-${currentBranch || 'none'}`}
        projectId={projectId}
        containerId={selectedContainerId}
        currentBranch={currentBranch}
        onUpdate={handleBranchUpdate}
        className="w-full"
        branches={paginatedBranches}
        pagination={pagination}
        onFetchBranches={handleFetchBranches}
        isFetchingBranches={containerData.isFetching}
        repoError={containerData.error}
        onForceRefreshBranches={handleForceRefreshBranches}
      />
    );
  };

  const handleRepoDetailsOpen = (containerId) => {
    setSelectedContainer(containerList.data.containers.find(c => c.id === containerId));
    setSelectedContainerId(containerId);
    fetchRepositoryDetails(containerId, 'view');
  };

  const handleRepoDetailsClose = (success = false) => {
    setShowRepoDetails(false);
    if (success && selectedContainerId) {
      setIsRepoConfigured(true);
      showAlert('Repository configured successfully', 'success');
      fetchRepositoryDetails(selectedContainerId, 'refresh');
    }
  };

  const handleSetPastTasks = (tasks) => {
    setPastTasks([...tasks]);
  };

  const fetchPastTasks = async (currentSkip = 0, currentLimit = limit) => {
    if (!selectedContainerId) {
      console.warn('fetchPastTasks called without selectedContainerId');
      return;
    }

    setIsPastTaskLoading(true);
    try {
      const result = await getPastCodeGenerationTasks(
        projectId,
        selectedContainerId,
        currentLimit,
        currentSkip
      );
      handleSetPastTasks(result.tasks);
      setTotalCount(result.total_count);
      setSkip(currentSkip);
      setLimit(currentLimit);
    } catch (error) {
      console.error('Error fetching past tasks:', error, { selectedContainerId });
      showAlert("Failed to fetch past code generation tasks", "error");
    } finally {
      setIsPastTaskLoading(false);
    }
  };

  const handleViewPastCodeGeneration = async () => {
    await fetchPastTasks();
    setIsPastTasksCodeModalOpen(true);
  };

  const handlePageChange = async (newPage) => {
    const newSkip = (newPage - 1) * limit;
    await fetchPastTasks(newSkip, limit);
  };

  const handleLimitChange = async (newLimit) => {
    await fetchPastTasks(0, newLimit);
  };

  const fetchHistoryTasks = useCallback(async (page = 1, pageSize = 10, filters = {}, isRefresh = false) => {
    try {
      if (!isRefresh) {
        setIsHistoryLoading(true);
      }
      const skip = (page - 1) * pageSize;

      const queryParams = new URLSearchParams();
      if (filters.search) queryParams.append('search', filters.search);
      if (filters.type) queryParams.append('type', filters.type);
      if (filters.status) queryParams.append('status', filters.status);
      if (filters.created_at) {
        const formattedDate = dayjs(filters.created_at).format('YYYY-MM-DD');
        queryParams.append('created_at', formattedDate);
      }

      const tasks = await getPastCodeTasks(
        projectId,
        pageSize,
        skip,
        queryParams.toString()
      );

      const transformedData = transformSessionsResponse(tasks);
      setSessions(transformedData.sessions);
      setHistoryPagination(prev => ({
        ...prev,
        totalItems: transformedData.pagination.total_count
      }));
    } catch (error) {
      console.error('Error fetching history tasks:', error);
      throw error;
    } finally {
      if (!isRefresh) {
        setIsHistoryLoading(false);
      }
    }
  }, [projectId]);

  const handleDirectRefresh = useCallback(async () => {
    try {
      await fetchHistoryTasks(
        historyPagination.currentPage,
        historyPagination.pageSize,
        historyFilters
      );
    } catch (error) {
      throw error;
    }
  }, [fetchHistoryTasks, historyPagination.currentPage, historyPagination.pageSize, historyFilters]);

  const handleHistoryPageChange = (newPage) => {
    setHistoryPagination(prev => ({ ...prev, currentPage: newPage }));
  };

  const handleHistoryPageSizeChange = (newSize) => {
    setHistoryPagination(prev => ({
      ...prev,
      pageSize: newSize,
      currentPage: 1
    }));
  };

  const handleHistoryFilterChange = useCallback((newFilters) => {
    setHistoryFilters(newFilters);
    setHistoryPagination(prev => ({ ...prev, currentPage: 1 }));
  }, []);

  const handleHistoryOpen = () => {
    fetchHistoryTasks(historyPagination.currentPage, historyPagination.pageSize, historyFilters);
    setIsHistoryOpen(true);
  };

  const handleUpdateContainerList = () => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("discussion", "new");
    newSearchParams.set("node_id", projectId);
    newSearchParams.set("node_type", "Architecture");
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  const updateProps = {
    onUpdateClick: handleUpdateContainerList,
    buttonText: "Update Container List",
  };

  const handleBack = () => {
    router.back();
  };

  const HeaderSection = () => (
    <div className="flex flex-col space-y-4 top-1" style={{ zIndex: 5 }}>
      <div className="flex flex-col border border-gray-200">
        <div className="relative px-2.5 py-1 space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <IconButton
                icon={<ArrowLeft className="w-5 h-5 text-gray-600" />}
                tooltip="Go back"
                onClick={handleBack}
                className="hover:bg-gray-100"
              />
              <div className="flex items-center gap-2">
                <h2 className="typography-body-lg font-weight-semibold text-gray-800">
                  {containerList?.data?.systemContext?.properties?.Title || "List of all containers"}
                </h2>
                {containerList?.data?.systemContext?.properties?.Type && (
                  <div className="flex items-center gap-1">
                    <Badge type={"Containers List"} />
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-primary-100/50 via-primary-300/20 to-transparent"></div>
        </div>
      </div>
    </div>
  );

  useEffect(() => {
    fetchData();
  }, [projectId]);

  useEffect(() => {
    if (searchParams.get("task_id")) {
    setCodeGenSetupModal(false);
    setIsGeneratingCode(false);
    }
  },[searchParams])

  const handleSelectAll = () => {
    setSelectedContainers(prev => {
      const newAllContainers = !prev.all_containers;
      setIsAllSelected(newAllContainers);
      return {
        all_containers: newAllContainers,
        containers: []
      };
    });
  };

  const handleContainerSelect = (container) => {
    setSelectedContainers(prev => {
      const containerId = String(container.id);
      const currentContainers = prev.containers.map(id => String(id));
      
      if (prev.all_containers) {
        const allContainerIds = containerList.data.containers.map(c => String(c.id));
        return {
          all_containers: false,
          containers: allContainerIds.filter(id => id !== containerId)
        };
      } else {
        const newContainers = currentContainers.includes(containerId)
          ? currentContainers.filter(id => id !== containerId)
          : [...currentContainers, containerId];

        const allSelected = newContainers.length === containerList.data.containers.length;
        return {
          all_containers: allSelected,
          containers: allSelected ? [] : newContainers
        };
      }
    });
  };

  useEffect(() => {
    if (!selectedContainers.all_containers) {
      setIsAllSelected(
        selectedContainers.containers &&
        containerList?.data?.containers &&
        selectedContainers.containers.length === containerList.data.containers.length &&
        containerList.data.containers.length > 0
      );
    }
  }, [selectedContainers.containers, selectedContainers.all_containers, containerList?.data?.containers]);

  useEffect(() => {
    setSelectedContainers({
      all_containers: false,
      containers: []
    });
    setIsAllSelected(false);
  }, [projectId]);

  useEffect(() => {
    if (!isModal) return;

    const selectAllBtn = document.getElementById('generation-select-all-btn');
    const historyBtn = document.getElementById('generation-history-btn');
    const startSessionBtn = document.getElementById('generation-start-session-btn');

    if (selectAllBtn) {
      const checkbox = selectAllBtn.querySelector('div > div');
      const span = selectAllBtn.querySelector('span');

      if (selectedContainers.all_containers) {
        checkbox.classList.add('bg-primary', 'border-primary');
        checkbox.classList.remove('border-gray-400');
        checkbox.innerHTML = `
          <svg width="8" height="8" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M8.5 3L4 7.5L1.5 5" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
        `;
        span.textContent = 'Deselect All';
      } else {
        checkbox.classList.remove('bg-primary', 'border-primary');
        checkbox.classList.add('border-gray-400');
        checkbox.innerHTML = '';
        span.textContent = 'Select All';
      }

      selectAllBtn.onclick = handleSelectAll;
    }

    if (startSessionBtn) {
      const isDisabled = selectedContainers.all_containers
        ? false
        : selectedContainers.containers.length === 0;

      startSessionBtn.disabled = isDisabled || isGeneratingCode || isCheckingManifest;

      if (isCheckingManifest) {
        startSessionBtn.innerHTML = `
          <svg class="mr-1.5 w-3.5 h-3.5 animate-spin" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span>Checking Manifest...</span>
        `;
        startSessionBtn.classList.add('opacity-75', 'cursor-not-allowed');
        startSessionBtn.classList.remove('hover:bg-primary-600');
      } else if (isDisabled || isGeneratingCode) {
        startSessionBtn.classList.add('opacity-50', 'cursor-not-allowed');
        startSessionBtn.classList.remove('hover:bg-primary-600');
      } else {
        startSessionBtn.classList.remove('opacity-50', 'cursor-not-allowed', 'opacity-75');
        startSessionBtn.classList.add('hover:bg-primary-600');
      }

      if (isGeneratingCode) {
        startSessionBtn.innerHTML = `
          <svg class="mr-1.5 w-3.5 h-3.5 animate-spin" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span>Starting Session</span>
        `;
      } else if (!isCheckingManifest) {
        startSessionBtn.innerHTML = '<span>Start Session</span>';
      }

      startSessionBtn.onclick = handleStartCodeGeneration;
    }
  }, [selectedContainers, isGeneratingCode, isModal, isCheckingManifest]);

  useEffect(() => {
    fetchHistoryTasks(historyPagination.currentPage, historyPagination.pageSize, historyFilters);
  }, [projectId, historyPagination.currentPage, historyPagination.pageSize, historyFilters]);

  if (loading) {
    return <CardGroupSkeletonLoder />;
  }

  if (error) {
    return (
      <ErrorView
        title="Unable to Load Container List"
        message={en.UnableToLoadContainer}
        onRetry={() => fetchData()}
        panelType='main'
      />
    );
  }

  if (!containerList?.data?.containers?.length || (isModal && !(containerList?.data?.containers?.some(
  item => item.properties?.ContainerType?.toLowerCase() == "internal" || item.properties?.ContainerType === undefined
))
)) {
    return <EmptyStateView type="containers" />;
  }

  return (
    <div className="h-full">
      {!isModal && <HeaderSection />}
      
      {showBaner && (
        <RequirementsBanner value={`${reconfigCount} container${reconfigCount > 1 ? 's' : ''} `} />
      )}

      {isModal && <CodeGenerationInfo />}

      <GenericCardGrid
        isModal={isModal}
        data={containerList.data.containers.map(container => ({
          container_name: container.properties.Title,
          components: [{
            container: {
              title: container.properties.Title,
              id: container.id,
            },
            ...container,
            ...container.properties
          }]
        }))}
        onCardClick={(container) => handleRowClick(container.id)}
        actionButtons={[
          {
            icon: <Eye className="h-4 w-4" />,
            label: "View",
            onClick: (comp) => handleRowClick(comp.id),
            className: "bg-gray-50 text-gray-600 hover:bg-gray-100"
          },
          {
            icon: <FolderGit2 className="h-4 w-4" />,
            label: "Repository",
            onClick: (comp) => handleRepoDetailsOpen(comp.id),
            className: "bg-primary-50 text-primary hover:bg-primary-100"
          },
          {
            icon: <CodeXml className="h-4 w-4" />,
            label: "Config",
            onClick: (comp) => {
              setSelectedContainer(comp);
              setSelectedContainerId(comp.id);
              fetchRepositoryDetails(comp.id, 'config');
            },
            className: "bg-primary-50 text-primary-600 hover:bg-primary-100"
          }
        ]}
        uniquePageIdentifier={`arch-container-list-${projectId}`}
        loadingStates={loadingStates}
        selectedContainers={selectedContainers}
        onContainerSelect={handleContainerSelect}
      />

      {/* Repository Modal */}
      {showRepoDetails && selectedContainerId && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-4 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <RepositoryDetailsModal
              open={true}
              projectId={projectId}
              containerId={selectedContainerId}
              onClose={handleRepoDetailsClose}
              onSuccess={() => handleRepoDetailsClose(true)}
              branches={containerBranches[`container_${selectedContainerId}`]?.branches || []}
              selectedBranch={selectedContainer?.properties?.branch}
              onBranchSelect={handleBranchUpdate}
            />
          </div>
        </div>
      )}

      {/* Code Generation Setup Modal */}
      {codeGenSetupModal && selectedContainerId && (
        <CodeGenerationSetupModal
          onClose={() => setCodeGenSetupModal(false)}
          onConfigureRepo={() => handleRepoDetailsOpen(selectedContainerId)}
          BranchSelection={BranchSelection}
          currentPlatform={currentPlatform}
          onPlatformChange={handlePlatformChange}
          onConfirm={handleUpdateConfiguration}
          repository={repositoryState.data}
          currentBranch={selectedContainer?.properties?.branch}
          currentFramework={currentFramework}
          onFrameworkChange={handleFrameworkChange}
          isGeneratingCode={false}
          projectId={projectId}
          containerId={parseInt(selectedContainerId)}
          handleRepoChange={handleRepoChangeInModal}
          isModal={isModal}
          isConfigMode={true}
          hideActionButton={true}
          selectedContainers={selectedContainers.all_containers 
            ? containerList.data.containers.map(c => ({
                id: c.id,
                title: c.properties.Title,
                properties: c.properties
              }))
            : selectedContainers.containers.map(containerId => {
                const container = containerList.data.containers.find(c => String(c.id) === String(containerId));
                return container ? {
                  id: container.id,
                  title: container.properties.Title,
                  properties: container.properties
                } : null;
              }).filter(Boolean)
          }
          isMultiContainer={selectedContainers.all_containers || selectedContainers.containers.length > 1}
        />
      )}

      {/* Manifest Preview Modal */}
      {showManifestPreview && (
        <ManifestPreviewModal
          manifest={manifestData}
          onProceed={(updatedManifest) => proceedWithCodeGeneration(updatedManifest)}
          onCancel={() => setShowManifestPreview(false)}
          isLoading={isGeneratingCode}
          projectId={projectId}
          onManifestUpdate={setManifestData}
          logInfo={manifestLogInfo}
        />
      )}

      {/* Code Generation Full Screen Loader */}
      {isGeneratingCode && (
        <div className="fixed inset-0 bg-gray-900/50 backdrop-blur-sm flex items-center justify-center z-[60]">
          <div className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl mx-4 relative overflow-hidden">
            <div className="absolute top-0 left-0 w-full h-2 bg-gray-100">
              <div className="h-full transition-all duration-500 bg-primary w-3/4 animate-pulse" />
            </div>

            <div className="p-8 pt-12">
              <div className="text-center mb-8">
                <h2 className="typography-heading-2 font-weight-bold text-gray-900">Preparing Your Code Generation Lab</h2>
                <p className="mt-2 text-gray-600">
                  Please wait while we set up your code generation environment
                </p>
              </div>

              <div className="mb-6 p-6 text-center">
                <div className="flex flex-col items-center gap-4">
                  <RefreshCw className="w-8 h-8 animate-spin text-primary-600" />
                  <span className="typography-body text-gray-700">
                    Starting code generation...
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Code Generation Handler */}
      {isGeneratingCode && (
        <CodeGenerationHandler
          projectId={projectId}
          containerIds={selectedContainers.all_containers 
            ? containerList.data.containers.map(c => c.id)
            : selectedContainers.containers.map(id => parseInt(id))}
          onComplete={() => {
            setIsGeneratingCode(false);
          }}
        />
      )}

      {/* Code Generation Modal */}
      {isVisible && <CodeGenerationModal />}

      {/* Past Tasks Modal */}
      {isPastTasksCodeModalOpen && (
        <PastTasksModal
          isOpen={isPastTasksCodeModalOpen}
          onClose={() => setIsPastTasksCodeModalOpen(false)}
          tasks={pastTasks}
          totalCount={totalCount}
          limit={limit}
          skip={skip}
          onPageChange={handlePageChange}
          onLimitChange={handleLimitChange}
          isLoading={isPastTaskLoading}
        />
      )}

      {/* History Modal */}
      {isHistoryOpen && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center backdrop-blur-sm">
          <div className="bg-white rounded-lg w-[94vw] h-[94vh] flex flex-col shadow-xl">
            <div className="flex-1 overflow-hidden flex flex-col">
              <div className="flex-1 overflow-y-auto custom-scrollbar p-2">
                <Sessions
                  initialSessions={sessions}
                  isLoading={isHistoryLoading}
                  onFilterChange={handleHistoryFilterChange}
                  onRefresh={handleDirectRefresh}
                  onCloseModal={() => setIsHistoryOpen(false)}
                  compactMode={true}
                />
              </div>
              <div className="sticky bottom-0 bg-white border-t border-gray-200 py-3 px-4">
                <div className="flex items-center justify-between">
                  <div className="typography-caption text-gray-500">
                    Showing {sessions.length} of {historyPagination.totalItems} sessions
                  </div>
                  <Pagination
                    currentPage={historyPagination.currentPage}
                    pageCount={Math.max(1, Math.ceil(historyPagination.totalItems / historyPagination.pageSize))}
                    pageSize={historyPagination.pageSize}
                    totalItems={historyPagination.totalItems}
                    onPageChange={handleHistoryPageChange}
                    onPageSizeChange={handleHistoryPageSizeChange}
                    pageSizeOptions={[5, 10, 20, 50]}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ContainerList;