"use client";

import React, { useEffect, useState, useContext } from 'react';
import Image from 'next/image';
import useLocalStorage from "@/hooks/useLocalStorage";
import { useUser } from '@/components/Context/UserContext';
import { TopBarContext } from '@/components/Context/TopBarContext';
import Cookies from 'js-cookie';
import kavia<PERSON>ogo from "@/../public/logo/kavia_logo.svg";
import NextJSImage from "@/../public/images/nextjs.svg";
import ReactJSImage from "@/../public/images/react.svg";
import VueImage from "@/../public/images/vue.svg";
import AngularImage from "@/../public/images/angular_logo.svg"
import NuxtImage from "@/../public/images/nuxt_logo.svg"
import RemixImage from "@/../public/images/remix_logo.svg"
import RemotionImage from "@/../public/images/remotion_logo.png"
import SlidevImage from "@/../public/images/slidev_logo.svg"
import SvelteImage from "@/../public/images/svelte_logo.svg"
import ViteImage from "@/../public/images/vite_logo.svg"
import QwikImage from "@/../public/images/qwik_logo.svg"
import AstroImage from "@/../public/images/astro_logo.svg"
import AndroidImage from "@/../public/images/android_logo.svg"
import KotlinImage from "@/../public/images/kotlin_logo.svg"

import FlutterImage from "@/../public/images/flutter_logo.svg"
import FlaskImage from "@/../public/images/flask_logo.svg"
import FastAPIImage from "@/../public/images/fastapi.svg"
import DjangoImage from "@/../public/images/django_logo.svg"
import ExpressImage from "@/../public/images/expressjs_logo.svg"
import AppTypeSwitch from './build/SimpleEnterpriceSwitch';
import TextInput from './build/TextInput';
import BuildOptionsButtons from './build/BuildOptionsButtons';
import ProjectSelectionComponent from './build/ProjectSelectionComponent';
import StackOptions from './build/StackOptions';
import ProjectCreationModal from './build/ProjectCreationFlow';
import { buildOptions } from './build/BuildOptionsButtons';

import { fetchProjectBlueprint } from '@/services/projectService';
import { useRouter } from 'next/navigation';
import { AlertContext } from './NotificationAlertService/AlertList';
import { getOrganizationId } from '../utils/navigationHelpers';
import { useKaviaWebsiteData } from '@/hooks/useKaviaWebsiteData';

export const frameworks = [
  { key: "react", label: 'React JS', icon: ReactJSImage, type: 'web', isDefault: true },
  { key: "angular", label: 'Angular', icon: AngularImage, type: 'web' },
  { key: "astro", label: 'Astro', icon: AstroImage, type: 'web' },
  { key: "nextjs", label: 'Next JS', icon: NextJSImage, type: 'web' },
  { key: "qwik", label: 'Qwik', icon: QwikImage, type: 'web' },
  { key: "nuxt", label: 'Nuxt', icon: NuxtImage, type: 'web' },
  { key: "remix", label: 'Remix', icon: RemixImage, type: 'web' },
  { key: "remotion", label: 'Remotion', icon: RemotionImage, type: 'web' },
  { key: "slidev", label: 'Slidev', icon: SlidevImage, type: 'web' },
  { key: "svelte", label: 'Svelte', icon: SvelteImage, type: 'web' },
  { key: "vite", label: 'Vite', icon: ViteImage, type: 'web' },
  { key: "vue", label: 'Vue', icon: VueImage, type: 'web' },
  { key: "flutter", label: 'Flutter', icon: FlutterImage, type: 'mobile', isDefault: true },
  { key: "android", label: 'Android', icon: AndroidImage, type: ['mobile', 'native-app'] },
  { key: "kotlin", label: 'Kotlin', icon: KotlinImage, type: 'mobile' },
  { key: "flask", label: 'Flask', icon: FlaskImage, type: 'backend' },
  { key: "fastapi", label: 'FastAPI', icon: FastAPIImage, type: 'backend', isDefault: true },
  { key: "django", label: 'Django', icon: DjangoImage, type: 'backend' },
  { key: "express", label: 'Express.js', icon: ExpressImage, type: 'backend' },
];


const appTypeOptions = [
  { label: 'Apps' },
  { label: 'Projects' }
];

const getReactDefaultIndex = () => {
  return frameworks.findIndex(framework => framework.key === 'react');
};

const BuildContent = ({
  loggedInState,
  selectedType,
  setSelectedType,
  selectedBuildOption,
  setBuildOption,
  activeFramework,
  setActiveFramework,
  createProject: parentCreateProject,
  handleComplexProjectSubmit,
  isComplexProjectSubmitting,
  setIsModalOpen,
  isStreaming,
  loadingText,
  inputText,
  setInputText,
}) => {
  const [prompt] = useLocalStorage("prompt", "");
  const { addTab } = useContext(TopBarContext);
  const [isProjectCreationModalOpen, setIsProjectCreationModalOpen] = useState(false);

  const [blueprintData, setBlueprintData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isImplementing, setIsImplementing] = useState(false);
  const router = useRouter();
  const idToken = Cookies.get('idToken');
  const userId = Cookies.get('userId');
  const { showAlert } = useContext(AlertContext)
  const reactDefaultIndex = getReactDefaultIndex();

  // Hash data retrieval for cross-domain prefill
  const { data: hashData, hasData: hasHashData, loading: hashLoading, clear: clearHashData } = useKaviaWebsiteData();

  // Enhanced state persistence using localStorage
  const [persistedInputText, setPersistedInputText] = useLocalStorage("user_input_text", "");
  const [persistedFramework, setPersistedFramework] = useLocalStorage("selected_framework", { web: reactDefaultIndex });
  const [persistedBuildOption, setPersistedBuildOption] = useLocalStorage("selected_build_option", 0);
  const [persistedAppType, setPersistedAppType] = useLocalStorage("selected_app_type", 0);

  // Flag to prevent hash data processing loops
  const [hashDataProcessed, setHashDataProcessed] = useState(false);

  // Determine if light theme should be used
  const isLight = false; // Always dark mode

  // State for greeting to avoid hydration errors
  const [greeting, setGreeting] = useState("Welcome");

  // Update greeting on client-side only
  useEffect(() => {
    const hour = new Date().getHours();
    let timeGreeting = "Welcome";
    if (hour < 12) timeGreeting = "Good morning";
    else if (hour < 18) timeGreeting = "Good afternoon";
    else timeGreeting = "Good evening";

    setGreeting(timeGreeting);
  }, []);

  // Clear localStorage when page is closed or refreshed (but NOT when navigating to login/signup)
  useEffect(() => {
    // Only clear localStorage on actual page refresh or fresh load, not on navigation
    const handlePageShow = (event) => {
      // If this is a fresh page load (not from cache/navigation) and not coming from login pages
      if (!event.persisted) {
        const referrer = document.referrer;
        const isFromLogin = referrer.includes('/users/login') || referrer.includes('/users/sign_up');
        
        if (!isFromLogin) {
          localStorage.removeItem("user_input_text");
          localStorage.removeItem("selected_framework");
          localStorage.removeItem("selected_build_option");
          localStorage.removeItem("selected_app_type");
          console.log('Cleared localStorage on fresh page load');
        } else {
          console.log('Skipping localStorage clear - user returning from login');
        }
      }
    };

    window.addEventListener('pageshow', handlePageShow);

    return () => {
      window.removeEventListener('pageshow', handlePageShow);
    };
  }, []);

  // FIXED: Always restore persisted state on mount first
  useEffect(() => {
    console.log('Restoring persisted state...');
    
    // Always restore persisted state first
    if (persistedInputText) {
      console.log('Restoring input text:', persistedInputText);
      setInputText(persistedInputText);
    }

    if (persistedFramework !== null && persistedFramework !== undefined) {
      console.log('Restoring framework:', persistedFramework);
      setActiveFramework(persistedFramework);
    }

    if (persistedBuildOption !== null && persistedBuildOption !== undefined) {
      const buildOptionValue = Array.isArray(persistedBuildOption) ? persistedBuildOption[0] || 0 : persistedBuildOption;
      console.log('Restoring build option:', buildOptionValue);
      setBuildOption(buildOptionValue);
    }

    if (persistedAppType !== null && persistedAppType !== undefined) {
      console.log('Restoring app type:', persistedAppType);
      setSelectedType(persistedAppType);
    }

    // Handle legacy prompt format
    if (prompt && !persistedInputText) {
      try {
        const promptObj = JSON.parse(prompt);
        if (promptObj['requirement']) {
          setInputText(promptObj['requirement']);
          setPersistedInputText(promptObj['requirement']);
        }
        if (promptObj['framework']) {
          const frameworkIndex = promptObj['framework'] === 'React JS' ? 1 :
            promptObj['framework'] === 'Vue JS' ? 2 : 0;
          setActiveFramework({ web: frameworkIndex });
          setPersistedFramework({ web: frameworkIndex });
        }
      } catch (error) {
        console.warn('Error parsing legacy prompt:', error);
      }
    }
  }, []); // FIXED: Back to empty dependency array - only run on mount

  // FIXED: Handle hash data prefilling (this can override persisted state if hash data exists)
  useEffect(() => {
    if (!hashLoading && hasHashData && hashData && !hashDataProcessed) {
      console.log('Hash data received, overriding persisted state:', hashData);
      setHashDataProcessed(true); // Prevent processing multiple times
      
      // Prefill prompt text
      if (hashData.prompt) {
        setInputText(hashData.prompt);
        setPersistedInputText(hashData.prompt);
      }
      
      // Prefill stack selection (Apps/Projects)
      if (hashData.stack) {
        const stackIndex = hashData.stack === 'Apps' ? 0 : 1;
        setSelectedType(stackIndex);
        setPersistedAppType(stackIndex);
      }
      
      // Prefill platform selection
      if (hashData.platform) {
        let platformIndex = 0; // Default to Web
        switch (hashData.platform.toLowerCase()) {
          case 'web':
            platformIndex = 0;
            break;
          case 'mobile':
            platformIndex = 1;
            break;
          case 'backend':
            platformIndex = 2;
            break;
          case 'fullstack':
            platformIndex = 3;
            break;
        }
        setBuildOption(platformIndex);
        setPersistedBuildOption(platformIndex);
      }
      
      // Prefill framework selections
      const newFrameworkSelection = {};
      const platformLower = hashData.platform ? hashData.platform.toLowerCase() : '';

      if (hashData.frontendFramework) {
        const frontendFramework = frameworks.find(f => f.label === hashData.frontendFramework);
        if (frontendFramework) {
          const frameworkIndex = frameworks.indexOf(frontendFramework);
          if (platformLower === 'fullstack') {
            newFrameworkSelection['frontend'] = frameworkIndex;
          } else if (platformLower === 'web' || platformLower === 'mobile') {
            newFrameworkSelection[platformLower] = frameworkIndex;
          }
        }
      }

      if (hashData.backendFramework) {
        const backendFramework = frameworks.find(f => f.label === hashData.backendFramework);
        if (backendFramework) {
          const frameworkIndex = frameworks.indexOf(backendFramework);
          if (platformLower === 'fullstack') {
            newFrameworkSelection['backend'] = frameworkIndex;
          } else if (platformLower === 'backend') {
            newFrameworkSelection['backend'] = frameworkIndex;
          }
        }
      }

      if (Object.keys(newFrameworkSelection).length > 0) {
        setActiveFramework(newFrameworkSelection);
        setPersistedFramework(newFrameworkSelection);
      }
      
      // Clear hash data after prefilling to clean up URL
      setTimeout(() => {
        clearHashData();
      }, 1000);
    }
  }, [hashLoading, hasHashData, hashData, hashDataProcessed, clearHashData]); // FIXED: Removed idToken dependency

  // Auto-submit the form only after inputText is set and hash data is loaded
  useEffect(() => {
    if (!hashLoading && hasHashData && inputText && !isStreaming && hashDataProcessed) {
      console.log('Auto-submitting form with hash data');
      console.log('User tokens present:', { idToken: !!idToken, userId: !!userId });
      
      // Add a small delay for auto-submit to ensure everything is ready
      setTimeout(() => {
        console.log('Executing auto-submit...');
        handleSubmitAndFetchBlueprint();
      }, 500);
    }
  }, [hashLoading, hasHashData, inputText, isStreaming, hashDataProcessed]); // FIXED: Removed idToken/userId dependencies

  // FIXED: State persistence effects - save to localStorage whenever state changes
  useEffect(() => {
    if (inputText !== persistedInputText) {
      setPersistedInputText(inputText);
    }
  }, [inputText, persistedInputText, setPersistedInputText]);

  useEffect(() => {
    if (JSON.stringify(activeFramework) !== JSON.stringify(persistedFramework)) {
      setPersistedFramework(activeFramework);
    }
  }, [activeFramework, persistedFramework, setPersistedFramework]);

  useEffect(() => {
    if (selectedBuildOption !== persistedBuildOption) {
      const buildOptionValue = Array.isArray(selectedBuildOption) ? selectedBuildOption[0] || 0 : selectedBuildOption;
      setPersistedBuildOption(buildOptionValue);
    }
  }, [selectedBuildOption, persistedBuildOption, setPersistedBuildOption]);

  useEffect(() => {
    if (selectedType !== persistedAppType) {
      setPersistedAppType(selectedType);
    }
  }, [selectedType, persistedAppType, setPersistedAppType]);

  const { name } = useUser();

  // FIXED: Only clear persisted state after successful project implementation
  const clearPersistedState = () => {
    console.log('Clearing persisted state...');
    setPersistedInputText("");
    setPersistedFramework({ web: reactDefaultIndex });
    setPersistedBuildOption(0);
    setPersistedAppType(0);
    
    // Also clear localStorage directly
    localStorage.removeItem("user_input_text");
    localStorage.removeItem("selected_framework");
    localStorage.removeItem("selected_build_option");
    localStorage.removeItem("selected_app_type");
  };

  // FIXED: Better handling of login redirect and state persistence
  const handleSubmitAndFetchBlueprint = async () => {
    setIsLoading(true);

    // Track the start time to ensure minimum loading duration
    const startTime = Date.now();

    if (!idToken || !userId) {
      console.log('User not logged in, saving state and redirecting...');
      console.log('Current router state:', router);
      console.log('Window location:', window.location.href);
      
      // FIXED: Ensure state is saved before redirecting
      setPersistedInputText(inputText);
      setPersistedFramework(activeFramework);
      setPersistedBuildOption(Array.isArray(selectedBuildOption) ? selectedBuildOption[0] || 0 : selectedBuildOption);
      setPersistedAppType(selectedType);

      // Show alert immediately
      showAlert("Access denied. Please create an account or log in to proceed.", "info");
      
      // Stop loading immediately to prevent UI conflicts
      setIsLoading(false);
      
      // Redirect after a small delay to ensure localStorage is written and alert is shown
      setTimeout(() => {
        console.log('Attempting redirect to sign up...');
        try {
          // For website visitors, use more direct navigation
          if (hasHashData) {
            console.log('Website visitor detected, using window.location for redirect');
            window.location.href = '/users/sign_up';
          } else {
            router.push('/users/sign_up');
          }
          console.log('Router push called successfully');
        } catch (error) {
          console.error('Router push failed:', error);
          // Fallback to window.location if router fails
          window.location.href = '/users/sign_up';
        }
      }, 500); // Increased timeout for better reliability
      
      return;
    }

    if (Cookies.get('is_public_selected') === 'true') {
      Cookies.set('is_public_selected', 'false');
    }

    try {
      // Handle single selection format
      const selectedBuildOptionIndex = Array.isArray(selectedBuildOption) ? selectedBuildOption[0] || 0 : selectedBuildOption || 0;
      const selectedBuildType = buildOptions[selectedBuildOptionIndex] ? buildOptions[selectedBuildOptionIndex].id : 'web';

      // Handle framework selection based on new format
      let selectedFrameworks = {};

      if (typeof activeFramework === 'object' && activeFramework !== null) {
        // New format: object with build type keys
        selectedFrameworks = activeFramework;
      } else {
        // Legacy format: single number - convert to new format
        const frameworkIndex = typeof activeFramework === 'number' ? activeFramework : 0;
        const framework = frameworks[frameworkIndex] || frameworks.find(f => f.isDefault);

        if (framework) {
          const frameworkTypes = Array.isArray(framework.type) ? framework.type : [framework.type];
          frameworkTypes.forEach(type => {
            if (selectedBuildType === 'fullstack') {
              if (type === 'web' || type === 'mobile') {
                selectedFrameworks['frontend'] = frameworkIndex;
              } else if (type === 'backend') {
                selectedFrameworks['backend'] = frameworkIndex;
              }
            } else if (type === selectedBuildType) {
              selectedFrameworks[selectedBuildType] = frameworkIndex;
            }
          });
        }
      }

      // Initialize variables
      let frontendFramework = "";
      let backendFramework = "";
      let platform = [];

      if (selectedBuildType === 'fullstack') {
        // For fullstack, handle frontend and backend separately
        const frontendFrameworkIndex = selectedFrameworks['frontend'];
        const backendFrameworkIndex = selectedFrameworks['backend'];

        if (frontendFrameworkIndex !== undefined) {
          const fw = frameworks[frontendFrameworkIndex];
          if (fw) {
            frontendFramework = fw.label;
            // Determine the frontend platform type (web or mobile)
            const frameworkType = Array.isArray(fw.type) ? fw.type[0] : fw.type;
            if (frameworkType === 'mobile' || (Array.isArray(fw.type) && fw.type.includes('mobile'))) {
              platform.push('mobile');
            } else if (frameworkType === 'web' || (Array.isArray(fw.type) && fw.type.includes('web'))) {
              platform.push('web');
            }
          }
        }

        if (backendFrameworkIndex !== undefined) {
          const bw = frameworks[backendFrameworkIndex];
          if (bw) {
            backendFramework = bw.label;
            platform.push('backend');
          }
        }
      } else {
        // For non-fullstack options
        const frameworkIndex = selectedFrameworks[selectedBuildType];
        if (frameworkIndex !== undefined) {
          const framework = frameworks[frameworkIndex];
          if (framework) {
            const frameworkType = Array.isArray(framework.type) ? framework.type[0] : framework.type;

            if (frameworkType === 'web' || frameworkType === 'mobile' || (Array.isArray(framework.type) && (framework.type.includes('web') || framework.type.includes('mobile')))) {
              frontendFramework = framework.label;
            } else if (frameworkType === 'backend' || (Array.isArray(framework.type) && framework.type.includes('backend'))) {
              backendFramework = framework.label;
            }
          }
        }

        platform.push(selectedBuildType);
      }

      // Remove duplicates from platform array
      platform = [...new Set(platform)];

      // Get the mock data from our service, passing the selected framework and correct platform
      const blueprintData = await fetchProjectBlueprint(inputText, frontendFramework, backendFramework, platform);

      const project_id = blueprintData?.projectInfo?.id
      const projectName = blueprintData?.name
      sessionStorage.setItem('generated_project_id', blueprintData?.projectInfo?.id);
      
      const org_name = getOrganizationId()
      const newProjectUrl = `/${org_name}/project/${project_id}/overview`
      addTab(projectName, newProjectUrl);

      setBlueprintData(blueprintData);
      // FIXED: Don't clear persisted state here - only clear after successful implementation
      setIsProjectCreationModalOpen(true);

    } catch (error) {
      console.error("Error in handleSubmitAndFetchBlueprint:", error);

      // Ensure minimum loading duration (2 seconds) before showing error
      const elapsedTime = Date.now() - startTime;
      const minimumLoadingTime = 2000; // 2 seconds

      if (elapsedTime < minimumLoadingTime) {
        await new Promise(resolve => setTimeout(resolve, minimumLoadingTime - elapsedTime));
      }

      // Show a more user-friendly error message based on the error type
      const errorMessage = error.message || "Failed to generate project blueprint. Please try again.";

      // Use the existing alert context instead of native alert
      if (showAlert) {
        showAlert(errorMessage, "error");
      } else {
        alert(errorMessage);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // FIXED: Clear persisted state only after successful project implementation
  const handleStartImplementation = async (state) => {
    setIsImplementing(true);
    if (parentCreateProject) {
      try {
        // Get the most up-to-date blueprint data from the state object
        const updatedBlueprint = state?.projectBlueprint || blueprintData;

        if (!updatedBlueprint) {
          console.error('No blueprint data available');
          return;
        }

        // Call parent function with the complete updated blueprint (not just projectInfo)
        await parentCreateProject(updatedBlueprint);

        // FIXED: Clear persisted state only after successful implementation
        clearPersistedState();

        // Close the modal only after successful implementation
        setIsProjectCreationModalOpen(false);
      } catch (error) {
        console.error('Error creating project:', error);
        // Don't close the modal on error, so users can retry
      } finally {
        setIsImplementing(false);
      }
    }
  };

  return (
    <div className="h-screen overflow-y-auto scrollbar-hide" style={{
      scrollbarWidth: 'none', /* Firefox */
      msOverflowStyle: 'none', /* Internet Explorer 10+ */
    }}>
      <style jsx>{`
        div::-webkit-scrollbar {
          display: none; /* Safari and Chrome */
        }
      `}</style>
      <div className="min-h-full flex items-center justify-center py-8">
        <div className={`w-full max-w-2xl px-4 flex flex-col items-center text-white transition-colors duration-500 ease-in-out pb-16`}>
          <div className={`w-full flex justify-center mb-6 transition-all duration-500 ease-in-out ${loggedInState ? 'opacity-0 h-0 overflow-hidden' : 'opacity-100'}`}>
            <Image
              src={kaviaLogo}
              alt={"Kavia AI"}
              className={`transition-transform duration-500 ease-in-out ${!loggedInState ? 'w-16 h-16 max-w-16 max-h-16' : 'size-22'
                }`}
            />
          </div>

          <div className={`text-center mb-2 transition-opacity duration-500 ease-in-out ${loggedInState ? 'opacity-100' : 'opacity-0 h-0 overflow-hidden'}`}>
            <p className={`text-gray-300 typography-heading-2 font-weight-medium transition-colors duration-500 ease-in-out`}>
              {`${greeting}, ${name || 'welcome back'}`}
            </p>
          </div>

          <h1 className={`typography-heading-1 font-weight-light text-center text-white mb-8 transition-colors duration-500 ease-in-out`}>
            What do you want to build today?
          </h1>

          <AppTypeSwitch selectedType={selectedType} setSelectedType={setSelectedType} isStreaming={isStreaming} isLight={isLight} />

          <div className="w-full min-h-[400px] flex flex-col items-center">
            {appTypeOptions[selectedType].label === "Apps" ? (
              <>
                <TextInput
                  disabled={isStreaming || isLoading}
                  loadingText={isLoading ? "Generating blueprint..." : loadingText}
                  inputText={inputText}
                  setInputText={setInputText}
                  handleSubmit={handleSubmitAndFetchBlueprint}
                  isLight={isLight}
                  isMobileSelected={buildOptions[selectedBuildOption]?.id === 'mobile'}
                />

                <BuildOptionsButtons disabled={isStreaming} buildOption={selectedBuildOption} setBuildOption={setBuildOption} isLight={isLight} />
                {/* Scrollable container for framework options */}
                <div className="w-full overflow-y-auto max-h-[60vh] flex flex-col items-center">
                  <StackOptions
                    key={JSON.stringify(activeFramework)}
                    frameworks={frameworks}
                    activeFramework={activeFramework}
                    setActiveFramework={setActiveFramework}
                    isStreaming={isStreaming}
                    isLight={isLight}
                    buildOption={selectedBuildOption}
                  />
                </div>
              </>
            ) : (
              <ProjectSelectionComponent
                handleComplexProjectSubmit={handleComplexProjectSubmit}
                isComplexProjectSubmitting={isComplexProjectSubmitting}
                isLight={isLight}
                setIsModalOpen={setIsModalOpen}
              />
            )}
          </div>

          <ProjectCreationModal
            isOpen={isProjectCreationModalOpen}
            onClose={() => {
              setIsProjectCreationModalOpen(false);
              setBlueprintData(null); // Clear blueprint data when closing
            }}
            onStartImplementation={handleStartImplementation}
            initialBlueprint={blueprintData}
            frameworkOptions={frameworks}
            isImplementing={isImplementing}
          />
        </div>
      </div>
    </div>
  );
};

export default BuildContent;